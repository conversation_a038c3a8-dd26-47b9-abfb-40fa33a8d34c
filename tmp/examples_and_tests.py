"""
Examples and tests for the spatial reasoning task generation framework
Demonstrates usage of all components and provides comprehensive testing
"""

import json
import time
from typing import Dict, List, Any

from config import TaskConfig
from task_generator import SpatialReasoningTaskGenerator, GeneratedTask
from llm_integration import LLMTaskGenerator, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from task_validator import ComprehensiveTaskValidator
from sapien_integration import SAPIENEnvironment, SimulationDataCollector
from data_collection_pipeline import DataCollectionPipeline, PipelineConfig


def example_basic_task_generation():
    """
    Example 1: Basic task generation without LLM enhancement
    """
    print("=== Example 1: Basic Task Generation ===")
    
    # Initialize configuration and generator
    config = TaskConfig()
    generator = SpatialReasoningTaskGenerator(config)
    
    # Generate tasks for each level
    for level in [1, 2, 3, 4]:
        print(f"\nGenerating Level {level} tasks:")
        
        # Generate single task
        task_input = generator.generate_task_input(level, num_objects=level)
        task = generator.generate_complete_task(task_input)
        
        print(f"Task ID: {task.task_id}")
        print(f"Description: {task.description}")
        print(f"Complexity Score: {task.complexity_score}")
        print(f"Object Count: {task.object_count}")
        print(f"Success Criteria: {task.success_criteria[:2]}...")  # Show first 2
    
    # Generate batch of tasks
    print(f"\nGenerating batch of 10 tasks...")
    batch_tasks = generator.generate_batch_tasks(
        num_tasks=10,
        level_distribution={1: 0.3, 2: 0.3, 3: 0.2, 4: 0.2}
    )
    
    print(f"Generated {len(batch_tasks)} tasks")
    print(f"Generation statistics: {generator.get_generation_statistics()}")
    
    return batch_tasks


def example_llm_enhanced_generation():
    """
    Example 2: LLM-enhanced task generation
    """
    print("\n=== Example 2: LLM-Enhanced Task Generation ===")
    
    config = TaskConfig()
    llm_provider = MockLLMProvider(config)
    llm_generator = LLMTaskGenerator(config, llm_provider)
    
    # Generate intelligent tasks
    print("Generating LLM-enhanced tasks...")
    
    for level in [1, 2, 3, 4]:
        task = llm_generator.generate_intelligent_task(level)
        print(f"\nLevel {level} LLM Task:")
        print(f"Description: {task.description}")
        print(f"Enhanced: {task.simulation_config.get('llm_enhanced', False)}")
    
    # Generate batch with context variations
    context_variations = llm_generator.create_context_variations()
    batch_tasks = llm_generator.generate_batch_intelligent_tasks(
        num_tasks=5,
        context_variations=context_variations[:3]
    )
    
    print(f"\nGenerated {len(batch_tasks)} LLM-enhanced tasks")
    return batch_tasks


def example_task_validation():
    """
    Example 3: Task validation
    """
    print("\n=== Example 3: Task Validation ===")
    
    config = TaskConfig()
    generator = SpatialReasoningTaskGenerator(config)
    validator = ComprehensiveTaskValidator(config)
    
    # Generate some tasks to validate
    tasks = generator.generate_batch_tasks(5)
    
    print("Validating generated tasks...")
    
    for task in tasks:
        validation_result = validator.validate_task(task)
        
        print(f"\nTask {task.task_id}:")
        print(f"Valid: {validation_result.is_valid}")
        print(f"Confidence: {validation_result.confidence_score:.2f}")
        
        if validation_result.validation_errors:
            print(f"Errors: {validation_result.validation_errors}")
        
        if validation_result.validation_warnings:
            print(f"Warnings: {validation_result.validation_warnings[:2]}...")
    
    # Batch validation
    batch_result = validator.validate_batch(tasks)
    print(f"\nBatch validation summary:")
    print(f"Total tasks: {batch_result['summary']['total_tasks']}")
    print(f"Valid tasks: {batch_result['summary']['valid_tasks']}")
    print(f"Validation rate: {batch_result['summary']['validation_rate']:.2f}")


def example_simulation_execution():
    """
    Example 4: Simulation execution
    """
    print("\n=== Example 4: Simulation Execution ===")
    
    config = TaskConfig()
    generator = SpatialReasoningTaskGenerator(config)
    simulation_env = SAPIENEnvironment(config)
    
    # Generate a simple task
    task_input = generator.generate_task_input(level=2, num_objects=1)
    task = generator.generate_complete_task(task_input)
    
    print(f"Executing task: {task.description}")
    
    try:
        # Execute task in simulation
        result = simulation_env.execute_task(task)
        
        print(f"Execution result:")
        print(f"Success: {result.success}")
        print(f"Execution time: {result.execution_time:.2f}s")
        print(f"Performance metrics: {result.performance_metrics}")
        
        if not result.success:
            print(f"Failure reasons: {result.failure_reasons}")
    
    finally:
        simulation_env.close()


def example_data_collection():
    """
    Example 5: Data collection pipeline
    """
    print("\n=== Example 5: Data Collection Pipeline ===")
    
    # Configure pipeline for small-scale collection
    pipeline_config = PipelineConfig(
        num_tasks_per_level={1: 2, 2: 2, 3: 1, 4: 1},  # Small numbers for demo
        validation_enabled=True,
        llm_enhancement=False,
        parallel_execution=False,  # Sequential for demo
        output_directory="demo_data"
    )
    
    config = TaskConfig()
    pipeline = DataCollectionPipeline(config, pipeline_config)
    
    print("Running data collection pipeline...")
    
    try:
        results = pipeline.run_full_pipeline()
        
        print(f"Pipeline completed!")
        print(f"Total execution time: {results['pipeline_stats']['execution_time']:.2f}s")
        print(f"Successful tasks: {results['successful_tasks']}")
        print(f"Failed tasks: {results['failed_tasks']}")
        print(f"Dataset saved to: {results['dataset_file']}")
        
        # Show collection statistics
        collection_stats = results['collection_stats']
        print(f"Collection statistics:")
        print(f"  Success rate: {collection_stats['success_rate']:.2f}")
        print(f"  Level distribution: {collection_stats['level_distribution']}")
        
    except Exception as e:
        print(f"Pipeline failed: {e}")


def test_framework_components():
    """
    Test suite for framework components
    """
    print("\n=== Testing Framework Components ===")
    
    config = TaskConfig()
    
    # Test 1: Configuration
    print("Test 1: Configuration loading...")
    assert len(config.MATERIALS) > 0
    assert len(config.COLORS) > 0
    assert len(config.TASK_TEMPLATES) == 4
    print("✓ Configuration test passed")
    
    # Test 2: Task generation
    print("Test 2: Task generation...")
    generator = SpatialReasoningTaskGenerator(config)
    
    for level in [1, 2, 3, 4]:
        task_input = generator.generate_task_input(level)
        assert task_input.level == level
        assert len(task_input.object_attributes) > 0
        
        task = generator.generate_complete_task(task_input)
        assert task.task_id
        assert task.description
        assert task.complexity_score > 0
    
    print("✓ Task generation test passed")
    
    # Test 3: Validation
    print("Test 3: Task validation...")
    validator = ComprehensiveTaskValidator(config)
    
    task = generator.generate_complete_task(generator.generate_task_input(2))
    validation_result = validator.validate_task(task)
    
    assert hasattr(validation_result, 'is_valid')
    assert hasattr(validation_result, 'confidence_score')
    assert 0 <= validation_result.confidence_score <= 1
    
    print("✓ Task validation test passed")
    
    # Test 4: LLM integration
    print("Test 4: LLM integration...")
    llm_provider = MockLLMProvider(config)
    assert llm_provider.is_available()
    
    llm_generator = LLMTaskGenerator(config, llm_provider)
    llm_task = llm_generator.generate_intelligent_task(1)
    
    assert llm_task.simulation_config.get('llm_enhanced', False)
    
    print("✓ LLM integration test passed")
    
    # Test 5: Data structures
    print("Test 5: Data structures...")
    
    # Test task serialization
    task_dict = task.to_dict()
    assert isinstance(task_dict, dict)
    assert 'task_id' in task_dict
    assert 'description' in task_dict
    
    print("✓ Data structures test passed")
    
    print("\n✅ All tests passed!")


def demo_complete_workflow():
    """
    Demonstration of complete workflow from task generation to data collection
    """
    print("\n=== Complete Workflow Demonstration ===")
    
    start_time = time.time()
    
    # Step 1: Setup
    print("Step 1: Setting up framework...")
    config = TaskConfig()
    
    # Step 2: Generate tasks
    print("Step 2: Generating diverse tasks...")
    generator = SpatialReasoningTaskGenerator(config)
    llm_generator = LLMTaskGenerator(config, MockLLMProvider(config))
    
    # Generate mix of regular and LLM-enhanced tasks
    regular_tasks = generator.generate_batch_tasks(3, {1: 0.33, 2: 0.33, 3: 0.34})
    llm_tasks = llm_generator.generate_batch_intelligent_tasks(2, {2: 0.5, 3: 0.5})
    
    all_tasks = regular_tasks + llm_tasks
    print(f"Generated {len(all_tasks)} tasks total")
    
    # Step 3: Validate tasks
    print("Step 3: Validating tasks...")
    validator = ComprehensiveTaskValidator(config)
    
    valid_tasks = []
    for task in all_tasks:
        validation_result = validator.validate_task(task)
        if validation_result.is_valid:
            valid_tasks.append(task)
    
    print(f"Validated {len(valid_tasks)} tasks")
    
    # Step 4: Execute in simulation (mock)
    print("Step 4: Executing tasks in simulation...")
    simulation_env = SAPIENEnvironment(config)
    data_collector = SimulationDataCollector(config, "demo_workflow_data")
    
    successful_executions = 0
    
    try:
        for i, task in enumerate(valid_tasks[:3]):  # Limit for demo
            print(f"  Executing task {i+1}/{min(3, len(valid_tasks))}")
            
            result = simulation_env.execute_task(task)
            data_collector.collect_task_execution_data(task, result)
            
            if result.success:
                successful_executions += 1
    
    finally:
        simulation_env.close()
    
    # Step 5: Save dataset
    print("Step 5: Saving collected data...")
    dataset_file = data_collector.save_dataset()
    
    # Summary
    end_time = time.time()
    execution_time = end_time - start_time
    
    print(f"\n🎉 Workflow completed in {execution_time:.2f} seconds!")
    print(f"📊 Results summary:")
    print(f"  - Generated tasks: {len(all_tasks)}")
    print(f"  - Validated tasks: {len(valid_tasks)}")
    print(f"  - Successful executions: {successful_executions}")
    print(f"  - Dataset saved to: {dataset_file}")
    
    return {
        "total_tasks": len(all_tasks),
        "valid_tasks": len(valid_tasks),
        "successful_executions": successful_executions,
        "execution_time": execution_time,
        "dataset_file": dataset_file
    }


if __name__ == "__main__":
    """
    Run all examples and tests
    """
    print("🚀 Spatial Reasoning Task Generation Framework")
    print("=" * 50)
    
    try:
        # Run examples
        example_basic_task_generation()
        example_llm_enhanced_generation()
        example_task_validation()
        example_simulation_execution()
        example_data_collection()
        
        # Run tests
        test_framework_components()
        
        # Run complete workflow demo
        workflow_results = demo_complete_workflow()
        
        print("\n" + "=" * 50)
        print("🎯 All examples and tests completed successfully!")
        print(f"📈 Workflow generated {workflow_results['total_tasks']} tasks")
        print(f"⏱️  Total execution time: {workflow_results['execution_time']:.2f}s")
        
    except Exception as e:
        print(f"\n❌ Error during execution: {e}")
        raise
