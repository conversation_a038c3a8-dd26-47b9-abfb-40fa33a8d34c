{"metadata": {"collection_stats": {"total_tasks_executed": 3, "successful_tasks": 0, "failed_tasks": 3, "total_data_points": 0}, "config": {"simulation_config": {"timestep": 0.004166666666666667, "max_episode_length": 1000, "camera_resolution": [640, 480], "render_mode": "rgb_array", "physics_engine": "physx"}, "data_collection_config": {"save_rgb": true, "save_depth": true, "save_segmentation": true, "save_point_cloud": true, "save_trajectory": true, "data_format": "hdf5", "compression": "gzip"}, "robot_config": {"robot_type": "panda", "control_mode": "position", "action_space_dim": 7, "gripper_enabled": true, "max_velocity": 1.0, "max_acceleration": 2.0}}, "total_entries": 3}, "data": [{"task_id": "cfe3f3f2-68da-4c93-b7e2-b2c30d7fe9c7", "task_level": 2, "task_description": "目标物体是紫色的陶瓷材质，位于边缘", "execution_success": false, "execution_time": 0.0, "timestamp": "2025-07-13T22:43:03.634969", "task_metadata": {"task_id": "cfe3f3f2-68da-4c93-b7e2-b2c30d7fe9c7", "level": 2, "description": "目标物体是紫色的陶瓷材质，位于边缘", "input_data": {"level": 2, "spatial_requirements": {"object_1": {"position": "边缘", "relative_position": "前方", "object_id": "obj_1_594ac990", "orientation": "垂直"}}, "object_attributes": {"object_1": {"material": "陶瓷", "color": "紫色", "shape": "椭圆体", "size": "中", "weight": "轻", "texture": "柔软", "transparency": "透明", "graspability": 0.8409959598128747}}, "constraints": {"stability": "必须稳定", "alignment": "对角", "collision_avoidance": true, "physics_enabled": true, "max_execution_time": 50.0}, "environment_context": {"location": "餐厅", "objects_in_room": ["角落", "边缘", "窗台"], "lighting": "昏暗", "space_size": "大", "temperature": "温暖", "noise_level": "嘈杂", "simulation_params": {"gravity": [0, 0, -9.81], "air_resistance": 0.059883481594588006, "surface_friction": 0.5194609537132131, "lighting_intensity": 0.8963841004814266, "camera_positions": [{"name": "front_view", "position": [0.8, 0.0, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "side_view", "position": [0.0, 0.8, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "top_view", "position": [0.0, 0.0, 1.2], "target": [0.0, 0.0, 0.3], "up": [1, 0, 0]}], "workspace_bounds": {"x_range": [-0.5, 0.5], "y_range": [-0.5, 0.5], "z_range": [0.0, 1.0]}}}, "task_id": "cfe3f3f2-68da-4c93-b7e2-b2c30d7fe9c7", "timestamp": "", "metadata": {"num_objects": 1, "generation_method": "automatic", "custom_params": {}}}, "complexity_score": 11.5, "estimated_difficulty": "中等", "spatial_constraints": ["object_1: 垂直", "stability: 必须稳定", "alignment: 对角"], "object_count": 1, "success_criteria": ["所有物体必须成功抓取和放置", "任务执行过程中无碰撞", "物体属性匹配要求", "相对位置关系正确"], "execution_steps": ["1. 初始化机械臂到准备位置", "2. 扫描工作空间，识别目标物体", "3. 定位紫色的陶瓷椭圆体", "4. 规划抓取路径并执行抓取", "5. 移动到目标位置: 边缘", "6. 精确放置并释放物体", "7. 验证任务完成情况", "8. 机械臂返回安全位置"], "simulation_config": {"timestep": 0.004166666666666667, "max_episode_length": 1000, "camera_resolution": [640, 480], "render_mode": "rgb_array", "physics_engine": "physx", "task_level": 2, "object_count": 1, "max_episode_steps": 1400, "success_threshold": 0.75, "environment_config": {"location": "餐厅", "objects_in_room": ["角落", "边缘", "窗台"], "lighting": "昏暗", "space_size": "大", "temperature": "温暖", "noise_level": "嘈杂", "simulation_params": {"gravity": [0, 0, -9.81], "air_resistance": 0.059883481594588006, "surface_friction": 0.5194609537132131, "lighting_intensity": 0.8963841004814266, "camera_positions": [{"name": "front_view", "position": [0.8, 0.0, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "side_view", "position": [0.0, 0.8, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "top_view", "position": [0.0, 0.0, 1.2], "target": [0.0, 0.0, 0.3], "up": [1, 0, 0]}], "workspace_bounds": {"x_range": [-0.5, 0.5], "y_range": [-0.5, 0.5], "z_range": [0.0, 1.0]}}}, "robot_config": {"robot_type": "panda", "control_mode": "position", "action_space_dim": 7, "gripper_enabled": true, "max_velocity": 1.0, "max_acceleration": 2.0}, "data_collection": {"save_rgb": true, "save_depth": true, "save_segmentation": true, "save_point_cloud": true, "save_trajectory": true, "data_format": "hdf5", "compression": "gzip"}}}, "simulation_metadata": {}, "performance_metrics": {}, "trajectory_file": "demo_workflow_data/trajectories/cfe3f3f2-68da-4c93-b7e2-b2c30d7fe9c7_trajectory.json"}, {"task_id": "e0604863-594e-477c-a8d8-3e6707b2af3e", "task_level": 2, "task_description": "将细长状形状的绿色物体放在边缘", "execution_success": false, "execution_time": 0.0, "timestamp": "2025-07-13T22:43:03.636238", "task_metadata": {"task_id": "e0604863-594e-477c-a8d8-3e6707b2af3e", "level": 2, "description": "将细长状形状的绿色物体放在边缘", "input_data": {"level": 2, "spatial_requirements": {"object_1": {"position": "边缘", "relative_position": "中间", "object_id": "obj_1_a3009b7c", "orientation": "水平"}, "object_2": {"position": "沙发", "relative_position": "附近", "object_id": "obj_2_9049ed65", "orientation": "倾斜"}}, "object_attributes": {"object_1": {"material": "金属", "color": "绿色", "shape": "细长状", "size": "大", "weight": "重", "texture": "坚硬", "transparency": "不透明", "graspability": 0.33890414216195586}, "object_2": {"material": "木质", "color": "白色", "shape": "球体", "size": "小", "weight": "轻", "texture": "粗糙", "transparency": "透明", "graspability": 0.4657415236422815}}, "constraints": {"stability": "允许轻微摆动", "alignment": "垂直", "collision_avoidance": true, "physics_enabled": true, "max_execution_time": 50.0}, "environment_context": {"location": "洗手间", "objects_in_room": ["沙发", "椅子", "角落"], "lighting": "昏暗", "space_size": "大", "temperature": "凉爽", "noise_level": "安静", "simulation_params": {"gravity": [0, 0, -9.81], "air_resistance": 0.034942184984201666, "surface_friction": 0.3670126868882659, "lighting_intensity": 0.5525327555640558, "camera_positions": [{"name": "front_view", "position": [0.8, 0.0, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "side_view", "position": [0.0, 0.8, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "top_view", "position": [0.0, 0.0, 1.2], "target": [0.0, 0.0, 0.3], "up": [1, 0, 0]}], "workspace_bounds": {"x_range": [-0.5, 0.5], "y_range": [-0.5, 0.5], "z_range": [0.0, 1.0]}}}, "task_id": "e0604863-594e-477c-a8d8-3e6707b2af3e", "timestamp": "", "metadata": {"num_objects": 2, "generation_method": "automatic", "custom_params": {}}}, "complexity_score": 13.0, "estimated_difficulty": "中等", "spatial_constraints": ["object_1: 水平", "object_2: 倾斜", "stability: 允许轻微摆动", "alignment: 垂直"], "object_count": 2, "success_criteria": ["所有物体必须成功抓取和放置", "任务执行过程中无碰撞", "物体属性匹配要求", "相对位置关系正确"], "execution_steps": ["1. 初始化机械臂到准备位置", "2. 扫描工作空间，识别目标物体", "3. 定位绿色的金属细长状", "4. 规划抓取路径并执行抓取", "5. 移动到目标位置: 边缘", "6. 精确放置并释放物体", "4. 定位白色的木质球体", "5. 规划抓取路径并执行抓取", "6. 移动到目标位置: 沙发", "7. 精确放置并释放物体", "11. 验证任务完成情况", "12. 机械臂返回安全位置"], "simulation_config": {"timestep": 0.004166666666666667, "max_episode_length": 1000, "camera_resolution": [640, 480], "render_mode": "rgb_array", "physics_engine": "physx", "task_level": 2, "object_count": 2, "max_episode_steps": 1400, "success_threshold": 0.75, "environment_config": {"location": "洗手间", "objects_in_room": ["沙发", "椅子", "角落"], "lighting": "昏暗", "space_size": "大", "temperature": "凉爽", "noise_level": "安静", "simulation_params": {"gravity": [0, 0, -9.81], "air_resistance": 0.034942184984201666, "surface_friction": 0.3670126868882659, "lighting_intensity": 0.5525327555640558, "camera_positions": [{"name": "front_view", "position": [0.8, 0.0, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "side_view", "position": [0.0, 0.8, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "top_view", "position": [0.0, 0.0, 1.2], "target": [0.0, 0.0, 0.3], "up": [1, 0, 0]}], "workspace_bounds": {"x_range": [-0.5, 0.5], "y_range": [-0.5, 0.5], "z_range": [0.0, 1.0]}}}, "robot_config": {"robot_type": "panda", "control_mode": "position", "action_space_dim": 7, "gripper_enabled": true, "max_velocity": 1.0, "max_acceleration": 2.0}, "data_collection": {"save_rgb": true, "save_depth": true, "save_segmentation": true, "save_point_cloud": true, "save_trajectory": true, "data_format": "hdf5", "compression": "gzip"}}}, "simulation_metadata": {}, "performance_metrics": {}, "trajectory_file": "demo_workflow_data/trajectories/e0604863-594e-477c-a8d8-3e6707b2af3e_trajectory.json"}, {"task_id": "3d8ac91a-51c4-493e-afe0-fdff2d785023", "task_level": 2, "task_description": "目标物体是蓝色的皮革材质，位于地板", "execution_success": false, "execution_time": 0.0, "timestamp": "2025-07-13T22:43:03.637250", "task_metadata": {"task_id": "3d8ac91a-51c4-493e-afe0-fdff2d785023", "level": 2, "description": "目标物体是蓝色的皮革材质，位于地板", "input_data": {"level": 2, "spatial_requirements": {"object_1": {"position": "地板", "relative_position": "下方", "object_id": "obj_1_dce7a4dc", "orientation": "倾斜"}}, "object_attributes": {"object_1": {"material": "皮革", "color": "蓝色", "shape": "立方体", "size": "大", "weight": "中等", "texture": "光滑", "transparency": "不透明", "graspability": 0.5052794411841061}}, "constraints": {"stability": "允许轻微摆动", "alignment": "对角", "collision_avoidance": true, "physics_enabled": true, "max_execution_time": 50.0}, "environment_context": {"location": "阳台", "objects_in_room": ["门", "柜子", "桌子"], "lighting": "昏暗", "space_size": "中", "temperature": "温暖", "noise_level": "轻微噪音", "simulation_params": {"gravity": [0, 0, -9.81], "air_resistance": 0.05664457520698442, "surface_friction": 0.597981951153705, "lighting_intensity": 0.6437473356144239, "camera_positions": [{"name": "front_view", "position": [0.8, 0.0, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "side_view", "position": [0.0, 0.8, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "top_view", "position": [0.0, 0.0, 1.2], "target": [0.0, 0.0, 0.3], "up": [1, 0, 0]}], "workspace_bounds": {"x_range": [-0.5, 0.5], "y_range": [-0.5, 0.5], "z_range": [0.0, 1.0]}}}, "task_id": "3d8ac91a-51c4-493e-afe0-fdff2d785023", "timestamp": "", "metadata": {"num_objects": 1, "generation_method": "automatic", "custom_params": {}}}, "complexity_score": 11.5, "estimated_difficulty": "中等", "spatial_constraints": ["object_1: 倾斜", "stability: 允许轻微摆动", "alignment: 对角"], "object_count": 1, "success_criteria": ["所有物体必须成功抓取和放置", "任务执行过程中无碰撞", "物体属性匹配要求", "相对位置关系正确"], "execution_steps": ["1. 初始化机械臂到准备位置", "2. 扫描工作空间，识别目标物体", "3. 定位蓝色的皮革立方体", "4. 规划抓取路径并执行抓取", "5. 移动到目标位置: 地板", "6. 精确放置并释放物体", "7. 验证任务完成情况", "8. 机械臂返回安全位置"], "simulation_config": {"timestep": 0.004166666666666667, "max_episode_length": 1000, "camera_resolution": [640, 480], "render_mode": "rgb_array", "physics_engine": "physx", "task_level": 2, "object_count": 1, "max_episode_steps": 1400, "success_threshold": 0.75, "environment_config": {"location": "阳台", "objects_in_room": ["门", "柜子", "桌子"], "lighting": "昏暗", "space_size": "中", "temperature": "温暖", "noise_level": "轻微噪音", "simulation_params": {"gravity": [0, 0, -9.81], "air_resistance": 0.05664457520698442, "surface_friction": 0.597981951153705, "lighting_intensity": 0.6437473356144239, "camera_positions": [{"name": "front_view", "position": [0.8, 0.0, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "side_view", "position": [0.0, 0.8, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "top_view", "position": [0.0, 0.0, 1.2], "target": [0.0, 0.0, 0.3], "up": [1, 0, 0]}], "workspace_bounds": {"x_range": [-0.5, 0.5], "y_range": [-0.5, 0.5], "z_range": [0.0, 1.0]}}}, "robot_config": {"robot_type": "panda", "control_mode": "position", "action_space_dim": 7, "gripper_enabled": true, "max_velocity": 1.0, "max_acceleration": 2.0}, "data_collection": {"save_rgb": true, "save_depth": true, "save_segmentation": true, "save_point_cloud": true, "save_trajectory": true, "data_format": "hdf5", "compression": "gzip"}}}, "simulation_metadata": {}, "performance_metrics": {}, "trajectory_file": "demo_workflow_data/trajectories/3d8ac91a-51c4-493e-afe0-fdff2d785023_trajectory.json"}]}