"""
Configuration module for spatial reasoning task generation framework
"""

from typing import Dict, List, Any
from dataclasses import dataclass


@dataclass
class TaskConfig:
    """
    Configuration class containing all parameters needed for task generation
    """
    
    # Basic object attributes
    MATERIALS = [
        "金属", "塑料", "木质", "玻璃", "陶瓷", 
        "布料", "皮革", "纸质", "橡胶", "石材"
    ]
    
    COLORS = [
        "红色", "蓝色", "绿色", "黄色", "黑色", 
        "白色", "灰色", "棕色", "紫色", "橙色"
    ]
    
    SHAPES = [
        "立方体", "球体", "圆柱体", "长方体", "圆锥体",
        "三角体", "椭圆体", "不规则形状", "扁平状", "细长状"
    ]
    
    # Spatial locations and positions
    LOCATIONS = [
        "桌子", "椅子", "沙发", "床", "书架", "柜子", "窗台", 
        "地板", "墙壁", "门", "窗户", "角落", "中央", "边缘"
    ]
    
    RELATIVE_POSITIONS = [
        "左侧", "右侧", "前方", "后方", "上方", "下方",
        "旁边", "附近", "对面", "中间", "之间"
    ]
    
    # Distance and measurement constraints
    DISTANCE_CONSTRAINTS = [5, 10, 15, 20, 25, 30]  # in centimeters
    
    # Spatial relationships
    RELATIONSHIPS = [
        "对齐", "平行", "垂直", "相邻", "重叠", 
        "分离", "包围", "接触", "支撑", "悬挂"
    ]
    
    # Complex constraints for higher level tasks
    COMPLEX_CONSTRAINTS = [
        "保持稳定", "避免碰撞", "确保可见", "便于抓取",
        "符合重心要求", "满足美观要求", "便于操作", "节省空间"
    ]
    
    # Environment contexts
    ENVIRONMENTS = [
        "厨房", "客厅", "卧室", "书房", "餐厅", 
        "洗手间", "阳台", "走廊", "储藏室", "办公室"
    ]
    
    # Task templates for different levels
    TASK_TEMPLATES = {
        1: [
            "将物体放置在{location}",
            "目标物体位于{location1}",
            "将物体移动到{relative_position}的{location}",
        ],
        2: [
            "将{material}材质的物体放置在{location}的{relative_position}",
            "目标物体是{color}的{material}材质，位于{location}",
            "将{shape}形状的{color}物体放在{location}",
        ],
        3: [
            "将物体放置在{location}，与{reference_object}保持{distance}厘米距离",
            "目标物体位于{location}的{relative_position}，且与参考物体材质相同",
            "将物体放在{location}，确保与其他物体{relationship}",
        ],
        4: [
            "将两个物体分别放置在{location1}和{location2}，确保{constraint}",
            "多个物体需要满足{relationship}关系，并符合{complex_constraint}",
            "在{location1}和{location2}之间建立物体布局，满足复杂的空间约束",
        ]
    }
    
    # LLM generation parameters
    LLM_CONFIG = {
        "model_name": "gpt-4",
        "temperature": 0.7,
        "max_tokens": 500,
        "top_p": 0.9,
    }
    
    # Simulation parameters for SAPIEN integration
    SIMULATION_CONFIG = {
        "timestep": 1/240,  # 240 Hz simulation
        "max_episode_length": 1000,
        "camera_resolution": (640, 480),
        "render_mode": "rgb_array",
        "physics_engine": "physx",
    }
    
    # Robot configuration
    ROBOT_CONFIG = {
        "robot_type": "panda",  # Franka Panda arm
        "control_mode": "position",
        "action_space_dim": 7,  # 7-DOF arm
        "gripper_enabled": True,
        "max_velocity": 1.0,
        "max_acceleration": 2.0,
    }
    
    # Data collection parameters
    DATA_COLLECTION_CONFIG = {
        "save_rgb": True,
        "save_depth": True,
        "save_segmentation": True,
        "save_point_cloud": True,
        "save_trajectory": True,
        "data_format": "hdf5",
        "compression": "gzip",
    }
    
    # Task difficulty parameters
    DIFFICULTY_LEVELS = {
        1: {"max_objects": 1, "max_constraints": 1, "complexity_score": 1.0},
        2: {"max_objects": 2, "max_constraints": 2, "complexity_score": 2.0},
        3: {"max_objects": 3, "max_constraints": 4, "complexity_score": 3.5},
        4: {"max_objects": 5, "max_constraints": 6, "complexity_score": 5.0},
    }
