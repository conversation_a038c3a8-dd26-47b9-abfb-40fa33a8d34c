#!/usr/bin/env python3
"""
快速测试脚本 - 验证框架基础功能
"""

def test_basic_functionality():
    """测试基础功能"""
    print("🧪 开始测试基础功能...")
    
    try:
        # 测试配置导入
        from config import TaskConfig
        config = TaskConfig()
        print("✅ 配置模块导入成功")
        
        # 测试任务生成器
        from task_generator import SpatialReasoningTaskGenerator
        generator = SpatialReasoningTaskGenerator(config)
        print("✅ 任务生成器初始化成功")
        
        # 生成一个简单任务
        task_input = generator.generate_task_input(level=1, num_objects=1)
        task = generator.generate_complete_task(task_input)
        print("✅ 任务生成成功")
        
        # 显示任务信息
        print(f"\n📋 生成的任务信息:")
        print(f"   任务ID: {task.task_id}")
        print(f"   级别: Level {task.level}")
        print(f"   描述: {task.description}")
        print(f"   复杂度: {task.complexity_score}")
        print(f"   物体数量: {task.object_count}")
        
        # 测试LLM集成
        from llm_integration import LLMTaskGenerator, MockLLMProvider
        llm_provider = MockLLMProvider(config)
        llm_generator = LLMTaskGenerator(config, llm_provider)
        print("✅ LLM集成模块导入成功")
        
        # 测试任务验证
        from task_validator import ComprehensiveTaskValidator
        validator = ComprehensiveTaskValidator(config)
        validation_result = validator.validate_task(task)
        print("✅ 任务验证模块导入成功")
        print(f"   验证结果: {'有效' if validation_result.is_valid else '无效'}")
        print(f"   置信度: {validation_result.confidence_score:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_batch_generation():
    """测试批量生成"""
    print("\n🔄 测试批量任务生成...")
    
    try:
        from config import TaskConfig
        from task_generator import SpatialReasoningTaskGenerator
        
        config = TaskConfig()
        generator = SpatialReasoningTaskGenerator(config)
        
        # 生成批量任务
        tasks = generator.generate_batch_tasks(
            num_tasks=5,
            level_distribution={1: 0.4, 2: 0.4, 3: 0.2}
        )
        
        print(f"✅ 成功生成 {len(tasks)} 个任务")
        
        # 显示任务分布
        level_count = {}
        for task in tasks:
            level_count[task.level] = level_count.get(task.level, 0) + 1
        
        print(f"   级别分布: {level_count}")
        
        # 显示前3个任务
        print(f"\n📝 前3个任务示例:")
        for i, task in enumerate(tasks[:3]):
            print(f"   {i+1}. Level {task.level}: {task.description}")
        
        return True
        
    except Exception as e:
        print(f"❌ 批量生成测试失败: {e}")
        return False


def test_llm_enhancement():
    """测试LLM增强功能"""
    print("\n🤖 测试LLM增强功能...")
    
    try:
        from config import TaskConfig
        from llm_integration import LLMTaskGenerator, MockLLMProvider
        
        config = TaskConfig()
        llm_provider = MockLLMProvider(config)
        llm_generator = LLMTaskGenerator(config, llm_provider)
        
        # 生成LLM增强任务
        enhanced_task = llm_generator.generate_intelligent_task(level=2)
        
        print(f"✅ LLM增强任务生成成功")
        print(f"   任务描述: {enhanced_task.description}")
        print(f"   LLM增强: {enhanced_task.simulation_config.get('llm_enhanced', False)}")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM增强测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 空间推理任务生成框架 - 快速测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(test_basic_functionality())
    test_results.append(test_batch_generation())
    test_results.append(test_llm_enhancement())
    
    # 总结测试结果
    print("\n" + "=" * 50)
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    if passed_tests == total_tests:
        print(f"🎉 所有测试通过! ({passed_tests}/{total_tests})")
        print("✅ 框架基础功能正常，可以开始使用")
    else:
        print(f"⚠️  部分测试失败 ({passed_tests}/{total_tests})")
        print("❌ 请检查错误信息并修复问题")
    
    print("\n📚 使用说明:")
    print("   - 运行完整示例: python examples_and_tests.py")
    print("   - 交互式使用: python main.py interactive")
    print("   - 生成任务: python main.py generate --level 2 --num-tasks 5")
    print("   - 查看帮助: python main.py --help")


if __name__ == "__main__":
    main()
