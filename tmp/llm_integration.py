"""
LLM Integration module for intelligent task generation
Provides advanced task generation using large language models
"""

import json
import random
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from abc import ABC, abstractmethod
from task_generator import TaskInput, GeneratedTask, SpatialReasoningTaskGenerator
from config import TaskConfig


@dataclass
class LLMPrompt:
    """
    Structure for LLM prompts with context and parameters
    """
    system_prompt: str
    user_prompt: str
    temperature: float = 0.7
    max_tokens: int = 500
    context: Dict[str, Any] = None


class BaseLLMProvider(ABC):
    """
    Abstract base class for LLM providers
    """
    
    @abstractmethod
    def generate_response(self, prompt: LLMPrompt) -> str:
        """Generate response from LLM"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if LLM service is available"""
        pass


class MockLLMProvider(BaseLLMProvider):
    """
    Mock LLM provider for testing and development
    """
    
    def __init__(self, config: TaskConfig):
        self.config = config
        self.response_templates = {
            1: [
                "将{color}的{material}{shape}放置在{location}上",
                "目标物体是{color}色的{material}材质，需要放在{location}",
                "抓取{shape}形状的物体并移动到{location}位置"
            ],
            2: [
                "将{color}的{material}{shape}放置在{location}的{relative_position}，确保物体稳定",
                "目标是{material}材质的{color}{shape}，需要精确放置在{location}的{relative_position}",
                "抓取{color}的{shape}并小心放置到{location}，注意{material}材质的特性"
            ],
            3: [
                "将{color}的{material}{shape}放置在{location}，与参考物体保持{distance}的距离，确保{relationship}关系",
                "目标物体需要与其他物体形成{relationship}的空间关系，距离控制在{distance}",
                "精确放置{color}{shape}，满足距离约束{distance}和空间关系{relationship}"
            ],
            4: [
                "协调多个物体的放置，确保{color1}的{shape1}和{color2}的{shape2}分别位于{location1}和{location2}，满足复杂的空间约束",
                "执行多物体操作任务，需要同时考虑{constraint}和{relationship}，确保所有物体协调配置",
                "完成复杂的空间布局任务，涉及多个物体的精确定位和相互关系的建立"
            ]
        }
    
    def generate_response(self, prompt: LLMPrompt) -> str:
        """Generate mock response based on prompt context"""
        context = prompt.context or {}
        level = context.get("level", 1)
        
        templates = self.response_templates.get(level, self.response_templates[1])
        template = random.choice(templates)
        
        # Fill template with context values
        try:
            return template.format(**context)
        except KeyError:
            # Fallback if context doesn't have all required keys
            return f"Level {level} 任务：根据给定的空间要求和物体属性完成操作任务"
    
    def is_available(self) -> bool:
        return True


class OpenAIProvider(BaseLLMProvider):
    """
    OpenAI GPT provider (placeholder for actual implementation)
    """
    
    def __init__(self, api_key: str, model: str = "gpt-4"):
        self.api_key = api_key
        self.model = model
        # Note: Actual OpenAI integration would require openai library
        # This is a placeholder implementation
    
    def generate_response(self, prompt: LLMPrompt) -> str:
        """
        Generate response using OpenAI API
        Note: This is a placeholder - actual implementation would use openai library
        """
        # Placeholder implementation
        return "OpenAI integration not implemented - using mock response"
    
    def is_available(self) -> bool:
        # Check if API key is available and service is reachable
        return bool(self.api_key)


class LLMTaskGenerator:
    """
    Enhanced task generator using LLM for intelligent task creation
    """
    
    def __init__(self, config: TaskConfig = None, llm_provider: BaseLLMProvider = None):
        self.config = config or TaskConfig()
        self.base_generator = SpatialReasoningTaskGenerator(self.config)
        self.llm_provider = llm_provider or MockLLMProvider(self.config)
        
    def generate_intelligent_task(self, level: int, context: Dict[str, Any] = None) -> GeneratedTask:
        """
        Generate task using LLM for enhanced creativity and realism
        
        Args:
            level: Task complexity level (1-4)
            context: Additional context for task generation
            
        Returns:
            GeneratedTask: LLM-enhanced generated task
        """
        # First generate base task structure
        base_task_input = self.base_generator.generate_task_input(level)
        
        # Prepare context for LLM
        llm_context = self._prepare_llm_context(base_task_input, context)
        
        # Generate enhanced description using LLM
        prompt = self._create_task_prompt(base_task_input, llm_context)
        enhanced_description = self.llm_provider.generate_response(prompt)
        
        # Create enhanced task
        enhanced_task = self.base_generator.generate_complete_task(base_task_input)
        enhanced_task.description = enhanced_description
        
        # Add LLM-specific metadata
        enhanced_task.simulation_config["llm_enhanced"] = True
        enhanced_task.simulation_config["llm_provider"] = type(self.llm_provider).__name__
        
        return enhanced_task
    
    def _prepare_llm_context(self, task_input: TaskInput, additional_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Prepare context dictionary for LLM prompt"""
        context = {
            "level": task_input.level,
            "object_count": len(task_input.object_attributes)
        }
        
        # Extract object attributes
        if task_input.object_attributes:
            first_obj = list(task_input.object_attributes.values())[0]
            context.update({
                "color": first_obj.get("color", ""),
                "material": first_obj.get("material", ""),
                "shape": first_obj.get("shape", "")
            })
            
            if len(task_input.object_attributes) > 1:
                second_obj = list(task_input.object_attributes.values())[1]
                context.update({
                    "color1": first_obj.get("color", ""),
                    "shape1": first_obj.get("shape", ""),
                    "color2": second_obj.get("color", ""),
                    "shape2": second_obj.get("shape", "")
                })
        
        # Extract spatial requirements
        if task_input.spatial_requirements:
            first_spatial = list(task_input.spatial_requirements.values())[0]
            context.update({
                "location": first_spatial.get("position", ""),
                "relative_position": first_spatial.get("relative_position", ""),
                "distance": first_spatial.get("distance_constraint", "")
            })
            
            if len(task_input.spatial_requirements) > 1:
                second_spatial = list(task_input.spatial_requirements.values())[1]
                context.update({
                    "location1": first_spatial.get("position", ""),
                    "location2": second_spatial.get("position", "")
                })
        
        # Extract constraints
        if task_input.constraints:
            context.update({
                "relationship": task_input.constraints.get("relationship", ""),
                "constraint": task_input.constraints.get("complex_constraint", "")
            })
        
        # Add additional context
        if additional_context:
            context.update(additional_context)
        
        return context
    
    def _create_task_prompt(self, task_input: TaskInput, context: Dict[str, Any]) -> LLMPrompt:
        """Create LLM prompt for task generation"""
        
        system_prompt = """你是一个专业的机械臂操作任务设计专家。你需要根据给定的物体属性、空间要求和约束条件，生成清晰、具体、可执行的机械臂操作任务描述。

任务描述应该：
1. 明确指出要操作的物体及其属性
2. 清楚说明目标位置和空间关系
3. 包含必要的约束条件和注意事项
4. 使用自然、专业的中文表达
5. 确保任务在物理上可行且安全

请生成一个简洁但完整的任务描述。"""

        user_prompt = f"""请为以下机械臂操作任务生成描述：

任务等级：Level {task_input.level}
物体属性：{json.dumps(task_input.object_attributes, ensure_ascii=False, indent=2)}
空间要求：{json.dumps(task_input.spatial_requirements, ensure_ascii=False, indent=2)}
约束条件：{json.dumps(task_input.constraints, ensure_ascii=False, indent=2)}
环境背景：{json.dumps(task_input.environment_context, ensure_ascii=False, indent=2)}

请生成一个清晰、具体的任务描述："""

        return LLMPrompt(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            temperature=self.config.LLM_CONFIG["temperature"],
            max_tokens=self.config.LLM_CONFIG["max_tokens"],
            context=context
        )
    
    def generate_batch_intelligent_tasks(self, num_tasks: int, 
                                       level_distribution: Dict[int, float] = None,
                                       context_variations: List[Dict[str, Any]] = None) -> List[GeneratedTask]:
        """
        Generate batch of LLM-enhanced tasks with optional context variations
        
        Args:
            num_tasks: Number of tasks to generate
            level_distribution: Distribution across difficulty levels
            context_variations: List of context variations to apply
            
        Returns:
            List[GeneratedTask]: List of LLM-enhanced tasks
        """
        if level_distribution is None:
            level_distribution = {1: 0.25, 2: 0.25, 3: 0.25, 4: 0.25}
        
        tasks = []
        
        for i in range(num_tasks):
            # Select level
            level = random.choices(
                list(level_distribution.keys()),
                weights=list(level_distribution.values())
            )[0]
            
            # Select context variation if provided
            context = None
            if context_variations:
                context = random.choice(context_variations)
            
            # Generate task
            try:
                task = self.generate_intelligent_task(level, context)
                tasks.append(task)
            except Exception as e:
                print(f"Error generating LLM task {i}: {e}")
                # Fallback to base generator
                base_task_input = self.base_generator.generate_task_input(level)
                fallback_task = self.base_generator.generate_complete_task(base_task_input)
                tasks.append(fallback_task)
        
        return tasks
    
    def create_context_variations(self) -> List[Dict[str, Any]]:
        """Create predefined context variations for diverse task generation"""
        variations = [
            {"scenario": "kitchen_cleanup", "emphasis": "efficiency"},
            {"scenario": "laboratory_setup", "emphasis": "precision"},
            {"scenario": "warehouse_sorting", "emphasis": "speed"},
            {"scenario": "art_installation", "emphasis": "aesthetics"},
            {"scenario": "medical_preparation", "emphasis": "sterility"},
            {"scenario": "electronics_assembly", "emphasis": "delicacy"},
            {"scenario": "food_preparation", "emphasis": "hygiene"},
            {"scenario": "tool_organization", "emphasis": "accessibility"}
        ]
        return variations
    
    def get_llm_statistics(self) -> Dict[str, Any]:
        """Get statistics about LLM usage"""
        return {
            "provider": type(self.llm_provider).__name__,
            "provider_available": self.llm_provider.is_available(),
            "config": self.config.LLM_CONFIG
        }
