#!/usr/bin/env python3
"""
主入口文件 - 基于LLM的空间推理任务生成框架
提供命令行接口和交互式使用方式
"""

import argparse
import json
import sys
import os
from typing import Dict, List, Any

from config import TaskConfig
from task_generator import SpatialReasoningTaskGenerator
from llm_integration import LLMTaskGenerator, MockLLMProvider
from task_validator import ComprehensiveTaskValidator
from sapien_integration import SAPIENEnvironment, SimulationDataCollector
from data_collection_pipeline import DataCollectionPipeline, PipelineConfig


def generate_tasks_command(args):
    """生成任务命令"""
    print(f"生成 {args.num_tasks} 个任务 (Level {args.level})")
    
    config = TaskConfig()
    
    if args.llm_enhanced:
        print("使用LLM增强生成...")
        llm_provider = MockLLMProvider(config)
        generator = LLMTaskGenerator(config, llm_provider)
        tasks = generator.generate_batch_intelligent_tasks(
            args.num_tasks, 
            level_distribution={args.level: 1.0}
        )
    else:
        generator = SpatialReasoningTaskGenerator(config)
        tasks = generator.generate_batch_tasks(
            args.num_tasks,
            level_distribution={args.level: 1.0}
        )
    
    # 保存任务
    output_file = args.output or f"generated_tasks_level_{args.level}.json"
    generator.save_tasks_to_file(tasks, output_file)
    
    print(f"任务已保存到: {output_file}")
    
    # 显示统计信息
    for i, task in enumerate(tasks[:3]):  # 显示前3个任务
        print(f"\n任务 {i+1}:")
        print(f"  描述: {task.description}")
        print(f"  复杂度: {task.complexity_score}")
        print(f"  物体数量: {task.object_count}")


def validate_tasks_command(args):
    """验证任务命令"""
    print(f"验证任务文件: {args.input_file}")
    
    config = TaskConfig()
    generator = SpatialReasoningTaskGenerator(config)
    validator = ComprehensiveTaskValidator(config)
    
    # 加载任务
    tasks = generator.load_tasks_from_file(args.input_file)
    print(f"加载了 {len(tasks)} 个任务")
    
    # 验证任务
    validation_results = validator.validate_batch(tasks)
    
    print(f"\n验证结果:")
    print(f"  总任务数: {validation_results['summary']['total_tasks']}")
    print(f"  有效任务: {validation_results['summary']['valid_tasks']}")
    print(f"  验证通过率: {validation_results['summary']['validation_rate']:.2%}")
    print(f"  平均置信度: {validation_results['summary']['average_confidence']:.2f}")
    
    # 保存验证结果
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(validation_results, f, ensure_ascii=False, indent=2)
        print(f"验证结果已保存到: {args.output}")


def simulate_tasks_command(args):
    """仿真执行任务命令"""
    print(f"在仿真环境中执行任务: {args.input_file}")
    
    config = TaskConfig()
    generator = SpatialReasoningTaskGenerator(config)
    simulation_env = SAPIENEnvironment(config)
    data_collector = SimulationDataCollector(config, args.output_dir or "simulation_results")
    
    # 加载任务
    tasks = generator.load_tasks_from_file(args.input_file)
    print(f"加载了 {len(tasks)} 个任务")
    
    # 限制执行数量
    if args.max_tasks:
        tasks = tasks[:args.max_tasks]
        print(f"限制执行 {len(tasks)} 个任务")
    
    successful_count = 0
    
    try:
        for i, task in enumerate(tasks):
            print(f"\n执行任务 {i+1}/{len(tasks)}: {task.task_id}")
            
            # 执行任务
            result = simulation_env.execute_task(task)
            
            # 收集数据
            data_collector.collect_task_execution_data(task, result)
            
            if result.success:
                successful_count += 1
                print(f"  ✓ 成功 (用时: {result.execution_time:.2f}s)")
            else:
                print(f"  ✗ 失败: {result.failure_reasons}")
    
    finally:
        simulation_env.close()
    
    # 保存数据集
    dataset_file = data_collector.save_dataset()
    
    print(f"\n仿真完成:")
    print(f"  成功率: {successful_count}/{len(tasks)} ({successful_count/len(tasks):.2%})")
    print(f"  数据集保存到: {dataset_file}")


def run_pipeline_command(args):
    """运行完整数据收集管道"""
    print("运行完整数据收集管道...")
    
    # 解析任务分布
    level_distribution = {}
    if args.level_distribution:
        for item in args.level_distribution.split(','):
            level, count = item.split(':')
            level_distribution[int(level)] = int(count)
    else:
        level_distribution = {1: 10, 2: 10, 3: 5, 4: 5}
    
    # 配置管道
    pipeline_config = PipelineConfig(
        num_tasks_per_level=level_distribution,
        validation_enabled=not args.no_validation,
        llm_enhancement=args.llm_enhanced,
        parallel_execution=not args.sequential,
        max_workers=args.workers,
        output_directory=args.output_dir or "pipeline_data"
    )
    
    config = TaskConfig()
    pipeline = DataCollectionPipeline(config, pipeline_config)
    
    # 运行管道
    results = pipeline.run_full_pipeline()
    
    print(f"\n管道执行完成:")
    print(f"  执行时间: {results['pipeline_stats']['execution_time']:.2f}s")
    print(f"  生成任务: {results['pipeline_stats']['total_tasks_generated']}")
    print(f"  成功执行: {results['pipeline_stats']['successful_executions']}")
    print(f"  数据集文件: {results['dataset_file']}")


def interactive_mode():
    """交互式模式"""
    print("🚀 欢迎使用空间推理任务生成框架 - 交互式模式")
    print("输入 'help' 查看可用命令，输入 'quit' 退出")
    
    config = TaskConfig()
    generator = SpatialReasoningTaskGenerator(config)
    validator = ComprehensiveTaskValidator(config)
    
    while True:
        try:
            command = input("\n> ").strip().lower()
            
            if command == 'quit' or command == 'exit':
                print("再见!")
                break
            
            elif command == 'help':
                print("\n可用命令:")
                print("  generate <level> [num] - 生成指定级别的任务")
                print("  validate <task_id> - 验证指定任务")
                print("  show <task_id> - 显示任务详情")
                print("  stats - 显示生成统计")
                print("  clear - 清除历史任务")
                print("  help - 显示此帮助")
                print("  quit - 退出程序")
            
            elif command.startswith('generate'):
                parts = command.split()
                level = int(parts[1]) if len(parts) > 1 else 1
                num = int(parts[2]) if len(parts) > 2 else 1
                
                print(f"生成 {num} 个 Level {level} 任务...")
                tasks = generator.generate_batch_tasks(num, {level: 1.0})
                
                for task in tasks:
                    print(f"  任务 {task.task_id}: {task.description}")
            
            elif command.startswith('validate'):
                parts = command.split()
                if len(parts) > 1:
                    task_id = parts[1]
                    # 查找任务
                    task = None
                    for t in generator.task_history:
                        if t.task_id.endswith(task_id[-8:]):  # 匹配后8位
                            task = t
                            break
                    
                    if task:
                        result = validator.validate_task(task)
                        print(f"任务 {task_id} 验证结果:")
                        print(f"  有效性: {result.is_valid}")
                        print(f"  置信度: {result.confidence_score:.2f}")
                        if result.validation_errors:
                            print(f"  错误: {result.validation_errors}")
                    else:
                        print(f"未找到任务: {task_id}")
                else:
                    print("请指定任务ID")
            
            elif command == 'stats':
                stats = generator.get_generation_statistics()
                print(f"生成统计:")
                print(f"  总任务数: {stats['total_generated']}")
                print(f"  级别分布: {stats['level_distribution']}")
                print(f"  成功率: {stats['success_rate']:.2%}")
            
            elif command == 'clear':
                generator.task_history.clear()
                generator.generation_stats = {
                    "total_generated": 0,
                    "level_distribution": {1: 0, 2: 0, 3: 0, 4: 0},
                    "success_rate": 0.0
                }
                print("历史任务已清除")
            
            else:
                print(f"未知命令: {command}. 输入 'help' 查看可用命令")
        
        except KeyboardInterrupt:
            print("\n\n再见!")
            break
        except Exception as e:
            print(f"错误: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="基于LLM的空间推理任务生成框架",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main.py generate --level 2 --num-tasks 10 --llm-enhanced
  python main.py validate --input tasks.json --output validation_results.json
  python main.py simulate --input tasks.json --output-dir sim_data
  python main.py pipeline --level-distribution "1:10,2:10,3:5,4:5" --llm-enhanced
  python main.py interactive
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 生成任务命令
    gen_parser = subparsers.add_parser('generate', help='生成任务')
    gen_parser.add_argument('--level', type=int, default=1, choices=[1,2,3,4], help='任务级别')
    gen_parser.add_argument('--num-tasks', type=int, default=5, help='生成任务数量')
    gen_parser.add_argument('--llm-enhanced', action='store_true', help='使用LLM增强')
    gen_parser.add_argument('--output', help='输出文件路径')
    
    # 验证任务命令
    val_parser = subparsers.add_parser('validate', help='验证任务')
    val_parser.add_argument('--input-file', required=True, help='输入任务文件')
    val_parser.add_argument('--output', help='输出验证结果文件')
    
    # 仿真执行命令
    sim_parser = subparsers.add_parser('simulate', help='仿真执行任务')
    sim_parser.add_argument('--input-file', required=True, help='输入任务文件')
    sim_parser.add_argument('--output-dir', help='输出目录')
    sim_parser.add_argument('--max-tasks', type=int, help='最大执行任务数')
    
    # 管道命令
    pipe_parser = subparsers.add_parser('pipeline', help='运行完整管道')
    pipe_parser.add_argument('--level-distribution', help='级别分布 (格式: "1:10,2:10,3:5,4:5")')
    pipe_parser.add_argument('--llm-enhanced', action='store_true', help='使用LLM增强')
    pipe_parser.add_argument('--no-validation', action='store_true', help='跳过验证')
    pipe_parser.add_argument('--sequential', action='store_true', help='顺序执行')
    pipe_parser.add_argument('--workers', type=int, default=4, help='并行工作进程数')
    pipe_parser.add_argument('--output-dir', help='输出目录')
    
    # 交互式模式
    subparsers.add_parser('interactive', help='交互式模式')
    
    args = parser.parse_args()
    
    if args.command == 'generate':
        generate_tasks_command(args)
    elif args.command == 'validate':
        validate_tasks_command(args)
    elif args.command == 'simulate':
        simulate_tasks_command(args)
    elif args.command == 'pipeline':
        run_pipeline_command(args)
    elif args.command == 'interactive':
        interactive_mode()
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
