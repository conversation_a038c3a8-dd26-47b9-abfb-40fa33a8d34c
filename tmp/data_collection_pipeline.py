"""
Comprehensive data collection pipeline for spatial reasoning tasks
Orchestrates task generation, simulation execution, and data collection
"""

import os
import json
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import multiprocessing as mp

from task_generator import SpatialReasoningTaskGenerator, GeneratedTask
from llm_integration import LLMTaskGenerator
from task_validator import ComprehensiveTaskValidator
from sapien_integration import SAPIENEnvironment, SimulationDataCollector
from config import TaskConfig


@dataclass
class PipelineConfig:
    """
    Configuration for data collection pipeline
    """
    num_tasks_per_level: Dict[int, int]
    validation_enabled: bool = True
    llm_enhancement: bool = False
    parallel_execution: bool = True
    max_workers: int = 4
    output_directory: str = "collected_data"
    save_failed_tasks: bool = True
    retry_failed_tasks: bool = True
    max_retries: int = 3


class DataCollectionPipeline:
    """
    Main pipeline for automated data collection from spatial reasoning tasks
    """
    
    def __init__(self, config: TaskConfig = None, pipeline_config: PipelineConfig = None):
        self.config = config or TaskConfig()
        self.pipeline_config = pipeline_config or self._default_pipeline_config()
        
        # Initialize components
        self.task_generator = SpatialReasoningTaskGenerator(self.config)
        self.llm_generator = LLMTaskGenerator(self.config) if self.pipeline_config.llm_enhancement else None
        self.validator = ComprehensiveTaskValidator(self.config) if self.pipeline_config.validation_enabled else None
        self.simulation_env = SAPIENEnvironment(self.config)
        self.data_collector = SimulationDataCollector(self.config, self.pipeline_config.output_directory)
        
        # Pipeline state
        self.pipeline_stats = {
            "total_tasks_generated": 0,
            "total_tasks_validated": 0,
            "total_tasks_executed": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "validation_failures": 0,
            "execution_time": 0.0
        }
        
        self.failed_tasks = []
        self.successful_tasks = []
    
    def _default_pipeline_config(self) -> PipelineConfig:
        """Create default pipeline configuration"""
        return PipelineConfig(
            num_tasks_per_level={1: 25, 2: 25, 3: 25, 4: 25},
            validation_enabled=True,
            llm_enhancement=False,
            parallel_execution=True,
            max_workers=min(4, mp.cpu_count()),
            output_directory="collected_data"
        )
    
    def run_full_pipeline(self) -> Dict[str, Any]:
        """
        Run complete data collection pipeline
        
        Returns:
            Dict containing pipeline results and statistics
        """
        print("Starting data collection pipeline...")
        start_time = time.time()
        
        try:
            # Step 1: Generate tasks
            print("Step 1: Generating tasks...")
            tasks = self._generate_tasks()
            print(f"Generated {len(tasks)} tasks")
            
            # Step 2: Validate tasks (if enabled)
            if self.pipeline_config.validation_enabled:
                print("Step 2: Validating tasks...")
                validated_tasks = self._validate_tasks(tasks)
                print(f"Validated {len(validated_tasks)} tasks")
            else:
                validated_tasks = tasks
            
            # Step 3: Execute tasks in simulation
            print("Step 3: Executing tasks in simulation...")
            execution_results = self._execute_tasks(validated_tasks)
            print(f"Executed {len(execution_results)} tasks")
            
            # Step 4: Collect and save data
            print("Step 4: Collecting and saving data...")
            dataset_file = self._finalize_data_collection()
            
            # Calculate final statistics
            end_time = time.time()
            self.pipeline_stats["execution_time"] = end_time - start_time
            
            results = {
                "pipeline_stats": self.pipeline_stats,
                "dataset_file": dataset_file,
                "collection_stats": self.data_collector.get_collection_statistics(),
                "successful_tasks": len(self.successful_tasks),
                "failed_tasks": len(self.failed_tasks)
            }
            
            print(f"Pipeline completed in {self.pipeline_stats['execution_time']:.2f} seconds")
            return results
            
        except Exception as e:
            print(f"Pipeline failed with error: {e}")
            raise
        finally:
            self.simulation_env.close()
    
    def _generate_tasks(self) -> List[GeneratedTask]:
        """Generate tasks according to pipeline configuration"""
        all_tasks = []
        
        for level, num_tasks in self.pipeline_config.num_tasks_per_level.items():
            print(f"Generating {num_tasks} tasks for level {level}")
            
            if self.llm_generator and self.pipeline_config.llm_enhancement:
                # Use LLM-enhanced generation
                level_tasks = self.llm_generator.generate_batch_intelligent_tasks(
                    num_tasks, 
                    level_distribution={level: 1.0}
                )
            else:
                # Use standard generation
                level_tasks = self.task_generator.generate_batch_tasks(
                    num_tasks,
                    level_distribution={level: 1.0},
                    validate_tasks=False  # We'll validate separately if needed
                )
            
            all_tasks.extend(level_tasks)
            self.pipeline_stats["total_tasks_generated"] += len(level_tasks)
        
        return all_tasks
    
    def _validate_tasks(self, tasks: List[GeneratedTask]) -> List[GeneratedTask]:
        """Validate generated tasks"""
        validated_tasks = []
        
        for task in tasks:
            validation_result = self.validator.validate_task(task)
            
            if validation_result.is_valid:
                validated_tasks.append(task)
                self.pipeline_stats["total_tasks_validated"] += 1
            else:
                print(f"Task {task.task_id} failed validation: {validation_result.validation_errors}")
                self.pipeline_stats["validation_failures"] += 1
                
                if self.pipeline_config.save_failed_tasks:
                    self.failed_tasks.append({
                        "task": task,
                        "failure_reason": "validation_failed",
                        "validation_errors": validation_result.validation_errors
                    })
        
        return validated_tasks
    
    def _execute_tasks(self, tasks: List[GeneratedTask]) -> List[Dict[str, Any]]:
        """Execute tasks in simulation"""
        execution_results = []
        
        if self.pipeline_config.parallel_execution and len(tasks) > 1:
            # Parallel execution
            execution_results = self._execute_tasks_parallel(tasks)
        else:
            # Sequential execution
            execution_results = self._execute_tasks_sequential(tasks)
        
        return execution_results
    
    def _execute_tasks_sequential(self, tasks: List[GeneratedTask]) -> List[Dict[str, Any]]:
        """Execute tasks sequentially"""
        results = []
        
        for i, task in enumerate(tasks):
            print(f"Executing task {i+1}/{len(tasks)}: {task.task_id}")
            
            try:
                result = self._execute_single_task(task)
                results.append(result)
                
                if result["simulation_result"].success:
                    self.successful_tasks.append(task)
                    self.pipeline_stats["successful_executions"] += 1
                else:
                    self._handle_failed_task(task, result["simulation_result"])
                    self.pipeline_stats["failed_executions"] += 1
                
                self.pipeline_stats["total_tasks_executed"] += 1
                
            except Exception as e:
                print(f"Error executing task {task.task_id}: {e}")
                self._handle_failed_task(task, None, str(e))
                self.pipeline_stats["failed_executions"] += 1
        
        return results
    
    def _execute_tasks_parallel(self, tasks: List[GeneratedTask]) -> List[Dict[str, Any]]:
        """Execute tasks in parallel"""
        results = []
        
        with ThreadPoolExecutor(max_workers=self.pipeline_config.max_workers) as executor:
            # Submit all tasks
            future_to_task = {
                executor.submit(self._execute_single_task, task): task 
                for task in tasks
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_task):
                task = future_to_task[future]
                
                try:
                    result = future.result()
                    results.append(result)
                    
                    if result["simulation_result"].success:
                        self.successful_tasks.append(task)
                        self.pipeline_stats["successful_executions"] += 1
                    else:
                        self._handle_failed_task(task, result["simulation_result"])
                        self.pipeline_stats["failed_executions"] += 1
                    
                    self.pipeline_stats["total_tasks_executed"] += 1
                    
                except Exception as e:
                    print(f"Error executing task {task.task_id}: {e}")
                    self._handle_failed_task(task, None, str(e))
                    self.pipeline_stats["failed_executions"] += 1
        
        return results
    
    def _execute_single_task(self, task: GeneratedTask) -> Dict[str, Any]:
        """Execute a single task in simulation"""
        # Reset environment for clean execution
        self.simulation_env.reset_environment()
        
        # Execute task
        simulation_result = self.simulation_env.execute_task(task)
        
        # Collect data
        dataset_entry = self.data_collector.collect_task_execution_data(task, simulation_result)
        
        return {
            "task": task,
            "simulation_result": simulation_result,
            "dataset_entry": dataset_entry
        }
    
    def _handle_failed_task(self, task: GeneratedTask, simulation_result=None, error_message: str = None):
        """Handle failed task execution"""
        failure_info = {
            "task": task,
            "failure_reason": "execution_failed",
            "error_message": error_message,
            "simulation_result": simulation_result
        }
        
        self.failed_tasks.append(failure_info)
        
        # Retry if configured
        if self.pipeline_config.retry_failed_tasks:
            # Implementation for retry logic would go here
            pass
    
    def _finalize_data_collection(self) -> str:
        """Finalize data collection and save dataset"""
        # Save main dataset
        dataset_file = self.data_collector.save_dataset()
        
        # Save pipeline metadata
        metadata_file = f"{self.pipeline_config.output_directory}/pipeline_metadata.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump({
                "pipeline_config": self.pipeline_config.__dict__,
                "pipeline_stats": self.pipeline_stats,
                "failed_tasks_count": len(self.failed_tasks),
                "successful_tasks_count": len(self.successful_tasks)
            }, f, ensure_ascii=False, indent=2)
        
        # Save failed tasks if any
        if self.failed_tasks and self.pipeline_config.save_failed_tasks:
            failed_tasks_file = f"{self.pipeline_config.output_directory}/failed_tasks.json"
            with open(failed_tasks_file, 'w', encoding='utf-8') as f:
                json.dump([{
                    "task_id": failure["task"].task_id,
                    "task_description": failure["task"].description,
                    "failure_reason": failure["failure_reason"],
                    "error_message": failure.get("error_message", "")
                } for failure in self.failed_tasks], f, ensure_ascii=False, indent=2)
        
        return dataset_file
    
    def run_custom_pipeline(self, custom_tasks: List[GeneratedTask]) -> Dict[str, Any]:
        """
        Run pipeline with custom task list
        
        Args:
            custom_tasks: List of pre-generated tasks
            
        Returns:
            Dict containing pipeline results
        """
        print(f"Running custom pipeline with {len(custom_tasks)} tasks...")
        
        start_time = time.time()
        
        try:
            # Validate if enabled
            if self.pipeline_config.validation_enabled:
                validated_tasks = self._validate_tasks(custom_tasks)
            else:
                validated_tasks = custom_tasks
            
            # Execute tasks
            execution_results = self._execute_tasks(validated_tasks)
            
            # Finalize
            dataset_file = self._finalize_data_collection()
            
            end_time = time.time()
            self.pipeline_stats["execution_time"] = end_time - start_time
            
            return {
                "pipeline_stats": self.pipeline_stats,
                "dataset_file": dataset_file,
                "collection_stats": self.data_collector.get_collection_statistics()
            }
            
        finally:
            self.simulation_env.close()
    
    def get_pipeline_summary(self) -> Dict[str, Any]:
        """Get comprehensive pipeline summary"""
        return {
            "pipeline_configuration": self.pipeline_config.__dict__,
            "execution_statistics": self.pipeline_stats,
            "data_collection_stats": self.data_collector.get_collection_statistics(),
            "task_generation_stats": self.task_generator.get_generation_statistics(),
            "failed_tasks_summary": len(self.failed_tasks),
            "successful_tasks_summary": len(self.successful_tasks)
        }
