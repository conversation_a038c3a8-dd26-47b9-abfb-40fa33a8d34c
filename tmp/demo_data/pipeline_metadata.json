{"pipeline_config": {"num_tasks_per_level": {"1": 2, "2": 2, "3": 1, "4": 1}, "validation_enabled": true, "llm_enhancement": false, "parallel_execution": false, "max_workers": 4, "output_directory": "demo_data", "save_failed_tasks": true, "retry_failed_tasks": true, "max_retries": 3}, "pipeline_stats": {"total_tasks_generated": 5, "total_tasks_validated": 3, "total_tasks_executed": 3, "successful_executions": 0, "failed_executions": 3, "validation_failures": 2, "execution_time": 0.0}, "failed_tasks_count": 5, "successful_tasks_count": 0}