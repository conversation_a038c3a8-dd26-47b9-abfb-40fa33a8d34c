{"metadata": {"collection_stats": {"total_tasks_executed": 3, "successful_tasks": 0, "failed_tasks": 3, "total_data_points": 0}, "config": {"simulation_config": {"timestep": 0.004166666666666667, "max_episode_length": 1000, "camera_resolution": [640, 480], "render_mode": "rgb_array", "physics_engine": "physx"}, "data_collection_config": {"save_rgb": true, "save_depth": true, "save_segmentation": true, "save_point_cloud": true, "save_trajectory": true, "data_format": "hdf5", "compression": "gzip"}, "robot_config": {"robot_type": "panda", "control_mode": "position", "action_space_dim": 7, "gripper_enabled": true, "max_velocity": 1.0, "max_acceleration": 2.0}}, "total_entries": 3}, "data": [{"task_id": "3dfa7e31-e9a6-4fa8-b4f8-2e4a337c3641", "task_level": 2, "task_description": "目标物体是棕色的木质材质，位于床", "execution_success": false, "execution_time": 0.0, "timestamp": "2025-07-13T22:43:03.627277", "task_metadata": {"task_id": "3dfa7e31-e9a6-4fa8-b4f8-2e4a337c3641", "level": 2, "description": "目标物体是棕色的木质材质，位于床", "input_data": {"level": 2, "spatial_requirements": {"object_1": {"position": "床", "relative_position": "下方", "object_id": "obj_1_b3fbb476", "orientation": "垂直"}, "object_2": {"position": "窗台", "relative_position": "前方", "object_id": "obj_2_59df861c", "orientation": "垂直"}}, "object_attributes": {"object_1": {"material": "木质", "color": "棕色", "shape": "球体", "size": "大", "weight": "重", "texture": "柔软", "transparency": "透明", "graspability": 0.495117290782994}, "object_2": {"material": "塑料", "color": "红色", "shape": "球体", "size": "大", "weight": "轻", "texture": "坚硬", "transparency": "透明", "graspability": 0.6247702047229127}}, "constraints": {"stability": "允许轻微摆动", "alignment": "对角", "collision_avoidance": true, "physics_enabled": true, "max_execution_time": 50.0}, "environment_context": {"location": "客厅", "objects_in_room": ["床", "桌子", "窗台"], "lighting": "明亮", "space_size": "中", "temperature": "温暖", "noise_level": "轻微噪音", "simulation_params": {"gravity": [0, 0, -9.81], "air_resistance": 0.05744207727623466, "surface_friction": 0.5743889356787337, "lighting_intensity": 0.7111276615733839, "camera_positions": [{"name": "front_view", "position": [0.8, 0.0, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "side_view", "position": [0.0, 0.8, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "top_view", "position": [0.0, 0.0, 1.2], "target": [0.0, 0.0, 0.3], "up": [1, 0, 0]}], "workspace_bounds": {"x_range": [-0.5, 0.5], "y_range": [-0.5, 0.5], "z_range": [0.0, 1.0]}}}, "task_id": "3dfa7e31-e9a6-4fa8-b4f8-2e4a337c3641", "timestamp": "", "metadata": {"num_objects": 2, "generation_method": "automatic", "custom_params": {}}}, "complexity_score": 13.0, "estimated_difficulty": "中等", "spatial_constraints": ["object_1: 垂直", "object_2: 垂直", "stability: 允许轻微摆动", "alignment: 对角"], "object_count": 2, "success_criteria": ["所有物体必须成功抓取和放置", "任务执行过程中无碰撞", "物体属性匹配要求", "相对位置关系正确"], "execution_steps": ["1. 初始化机械臂到准备位置", "2. 扫描工作空间，识别目标物体", "3. 定位棕色的木质球体", "4. 规划抓取路径并执行抓取", "5. 移动到目标位置: 床", "6. 精确放置并释放物体", "4. 定位红色的塑料球体", "5. 规划抓取路径并执行抓取", "6. 移动到目标位置: 窗台", "7. 精确放置并释放物体", "11. 验证任务完成情况", "12. 机械臂返回安全位置"], "simulation_config": {"timestep": 0.004166666666666667, "max_episode_length": 1000, "camera_resolution": [640, 480], "render_mode": "rgb_array", "physics_engine": "physx", "task_level": 2, "object_count": 2, "max_episode_steps": 1400, "success_threshold": 0.75, "environment_config": {"location": "客厅", "objects_in_room": ["床", "桌子", "窗台"], "lighting": "明亮", "space_size": "中", "temperature": "温暖", "noise_level": "轻微噪音", "simulation_params": {"gravity": [0, 0, -9.81], "air_resistance": 0.05744207727623466, "surface_friction": 0.5743889356787337, "lighting_intensity": 0.7111276615733839, "camera_positions": [{"name": "front_view", "position": [0.8, 0.0, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "side_view", "position": [0.0, 0.8, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "top_view", "position": [0.0, 0.0, 1.2], "target": [0.0, 0.0, 0.3], "up": [1, 0, 0]}], "workspace_bounds": {"x_range": [-0.5, 0.5], "y_range": [-0.5, 0.5], "z_range": [0.0, 1.0]}}}, "robot_config": {"robot_type": "panda", "control_mode": "position", "action_space_dim": 7, "gripper_enabled": true, "max_velocity": 1.0, "max_acceleration": 2.0}, "data_collection": {"save_rgb": true, "save_depth": true, "save_segmentation": true, "save_point_cloud": true, "save_trajectory": true, "data_format": "hdf5", "compression": "gzip"}}}, "simulation_metadata": {}, "performance_metrics": {}, "trajectory_file": "demo_data/trajectories/3dfa7e31-e9a6-4fa8-b4f8-2e4a337c3641_trajectory.json"}, {"task_id": "c4e75d1f-95a8-4742-8394-668dee0872d3", "task_level": 2, "task_description": "将长方体形状的灰色物体放在窗台", "execution_success": false, "execution_time": 0.0, "timestamp": "2025-07-13T22:43:03.628462", "task_metadata": {"task_id": "c4e75d1f-95a8-4742-8394-668dee0872d3", "level": 2, "description": "将长方体形状的灰色物体放在窗台", "input_data": {"level": 2, "spatial_requirements": {"object_1": {"position": "窗台", "relative_position": "对面", "object_id": "obj_1_ccfce757", "orientation": "垂直"}, "object_2": {"position": "边缘", "relative_position": "中间", "object_id": "obj_2_82e9d4cc", "orientation": "垂直"}}, "object_attributes": {"object_1": {"material": "金属", "color": "灰色", "shape": "长方体", "size": "大", "weight": "轻", "texture": "光滑", "transparency": "透明", "graspability": 0.8452782945539707}, "object_2": {"material": "玻璃", "color": "蓝色", "shape": "立方体", "size": "小", "weight": "重", "texture": "光滑", "transparency": "不透明", "graspability": 0.46294827965439855}}, "constraints": {"stability": "允许轻微摆动", "alignment": "水平", "collision_avoidance": true, "physics_enabled": true, "max_execution_time": 50.0}, "environment_context": {"location": "办公室", "objects_in_room": ["墙壁", "边缘", "窗台"], "lighting": "自然光", "space_size": "小", "temperature": "常温", "noise_level": "嘈杂", "simulation_params": {"gravity": [0, 0, -9.81], "air_resistance": 0.03397614706330772, "surface_friction": 0.7803464003496292, "lighting_intensity": 0.7500999930496218, "camera_positions": [{"name": "front_view", "position": [0.8, 0.0, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "side_view", "position": [0.0, 0.8, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "top_view", "position": [0.0, 0.0, 1.2], "target": [0.0, 0.0, 0.3], "up": [1, 0, 0]}], "workspace_bounds": {"x_range": [-0.5, 0.5], "y_range": [-0.5, 0.5], "z_range": [0.0, 1.0]}}}, "task_id": "c4e75d1f-95a8-4742-8394-668dee0872d3", "timestamp": "", "metadata": {"num_objects": 2, "generation_method": "automatic", "custom_params": {}}}, "complexity_score": 13.0, "estimated_difficulty": "中等", "spatial_constraints": ["object_1: 垂直", "object_2: 垂直", "stability: 允许轻微摆动", "alignment: 水平"], "object_count": 2, "success_criteria": ["所有物体必须成功抓取和放置", "任务执行过程中无碰撞", "物体属性匹配要求", "相对位置关系正确"], "execution_steps": ["1. 初始化机械臂到准备位置", "2. 扫描工作空间，识别目标物体", "3. 定位灰色的金属长方体", "4. 规划抓取路径并执行抓取", "5. 移动到目标位置: 窗台", "6. 精确放置并释放物体", "4. 定位蓝色的玻璃立方体", "5. 规划抓取路径并执行抓取", "6. 移动到目标位置: 边缘", "7. 精确放置并释放物体", "11. 验证任务完成情况", "12. 机械臂返回安全位置"], "simulation_config": {"timestep": 0.004166666666666667, "max_episode_length": 1000, "camera_resolution": [640, 480], "render_mode": "rgb_array", "physics_engine": "physx", "task_level": 2, "object_count": 2, "max_episode_steps": 1400, "success_threshold": 0.75, "environment_config": {"location": "办公室", "objects_in_room": ["墙壁", "边缘", "窗台"], "lighting": "自然光", "space_size": "小", "temperature": "常温", "noise_level": "嘈杂", "simulation_params": {"gravity": [0, 0, -9.81], "air_resistance": 0.03397614706330772, "surface_friction": 0.7803464003496292, "lighting_intensity": 0.7500999930496218, "camera_positions": [{"name": "front_view", "position": [0.8, 0.0, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "side_view", "position": [0.0, 0.8, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "top_view", "position": [0.0, 0.0, 1.2], "target": [0.0, 0.0, 0.3], "up": [1, 0, 0]}], "workspace_bounds": {"x_range": [-0.5, 0.5], "y_range": [-0.5, 0.5], "z_range": [0.0, 1.0]}}}, "robot_config": {"robot_type": "panda", "control_mode": "position", "action_space_dim": 7, "gripper_enabled": true, "max_velocity": 1.0, "max_acceleration": 2.0}, "data_collection": {"save_rgb": true, "save_depth": true, "save_segmentation": true, "save_point_cloud": true, "save_trajectory": true, "data_format": "hdf5", "compression": "gzip"}}}, "simulation_metadata": {}, "performance_metrics": {}, "trajectory_file": "demo_data/trajectories/c4e75d1f-95a8-4742-8394-668dee0872d3_trajectory.json"}, {"task_id": "9c706e74-2a75-4910-ad76-ba9b3388ca5b", "task_level": 4, "task_description": "多个物体需要满足分离关系，并符合满足美观要求", "execution_success": false, "execution_time": 0.0, "timestamp": "2025-07-13T22:43:03.630089", "task_metadata": {"task_id": "9c706e74-2a75-4910-ad76-ba9b3388ca5b", "level": 4, "description": "多个物体需要满足分离关系，并符合满足美观要求", "input_data": {"level": 4, "spatial_requirements": {"object_1": {"position": "边缘", "relative_position": "中间", "object_id": "obj_1_e2dad899", "orientation": "垂直", "distance_constraint": "5厘米", "precision_level": "粗略"}, "object_2": {"position": "书架", "relative_position": "左侧", "object_id": "obj_2_63ab938c", "orientation": "水平", "distance_constraint": "25厘米", "precision_level": "精确", "reference_object": "object_1", "spatial_relationship": "接触"}, "object_3": {"position": "床", "relative_position": "下方", "object_id": "obj_3_f5b04d17", "orientation": "垂直", "distance_constraint": "15厘米", "precision_level": "高精度", "reference_object": "object_2", "spatial_relationship": "分离"}, "object_4": {"position": "沙发", "relative_position": "对面", "object_id": "obj_4_aff27837", "orientation": "水平", "distance_constraint": "5厘米", "precision_level": "粗略", "reference_object": "object_3", "spatial_relationship": "包围"}}, "object_attributes": {"object_1": {"material": "陶瓷", "color": "棕色", "shape": "立方体", "size": "小", "weight": "轻", "texture": "粗糙", "transparency": "透明", "graspability": 0.9852419999295963}, "object_2": {"material": "布料", "color": "蓝色", "shape": "细长状", "size": "小", "weight": "中等", "texture": "粗糙", "transparency": "透明", "graspability": 0.39621715386878076}, "object_3": {"material": "石材", "color": "黑色", "shape": "圆柱体", "size": "大", "weight": "重", "texture": "坚硬", "transparency": "透明", "graspability": 0.7766029954524338}, "object_4": {"material": "玻璃", "color": "白色", "shape": "细长状", "size": "中", "weight": "重", "texture": "柔软", "transparency": "不透明", "graspability": 0.416638595858433}}, "constraints": {"stability": "允许轻微摆动", "alignment": "对角", "collision_avoidance": true, "distance": "20厘米", "relationship": "分离", "precision_tolerance": 0.013931016680864348, "complex_constraint": "保持稳定", "multi_object_coordination": true, "temporal_constraints": "同时执行", "physics_enabled": true, "max_execution_time": 70.0}, "environment_context": {"location": "客厅", "objects_in_room": ["墙壁", "窗户", "椅子"], "lighting": "昏暗", "space_size": "中", "temperature": "凉爽", "noise_level": "嘈杂", "simulation_params": {"gravity": [0, 0, -9.81], "air_resistance": 0.04670171494454666, "surface_friction": 0.5119604117091148, "lighting_intensity": 0.8984080670213357, "camera_positions": [{"name": "front_view", "position": [0.8, 0.0, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "side_view", "position": [0.0, 0.8, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "top_view", "position": [0.0, 0.0, 1.2], "target": [0.0, 0.0, 0.3], "up": [1, 0, 0]}], "workspace_bounds": {"x_range": [-0.5, 0.5], "y_range": [-0.5, 0.5], "z_range": [0.0, 1.0]}}}, "task_id": "9c706e74-2a75-4910-ad76-ba9b3388ca5b", "timestamp": "", "metadata": {"num_objects": 4, "generation_method": "automatic", "custom_params": {}}}, "complexity_score": 27.2, "estimated_difficulty": "非常困难", "spatial_constraints": ["object_1: 5厘米", "object_1: 垂直", "object_2: 25厘米", "object_2: 水平", "object_3: 15厘米", "object_3: 垂直", "object_4: 5厘米", "object_4: 水平", "stability: 允许轻微摆动", "alignment: 对角", "distance: 20厘米", "relationship: 分离", "complex_constraint: 保持稳定", "temporal_constraints: 同时执行"], "object_count": 4, "success_criteria": ["所有物体必须成功抓取和放置", "任务执行过程中无碰撞", "物体属性匹配要求", "相对位置关系正确", "距离约束满足", "空间关系准确", "多物体协调完成", "复杂约束全部满足"], "execution_steps": ["1. 初始化机械臂到准备位置", "2. 扫描工作空间，识别目标物体", "3. 定位棕色的陶瓷立方体", "4. 规划抓取路径并执行抓取", "5. 移动到目标位置: 边缘", "6. 精确放置并释放物体", "4. 定位蓝色的布料细长状", "5. 规划抓取路径并执行抓取", "6. 移动到目标位置: 书架", "7. 精确放置并释放物体", "5. 定位黑色的石材圆柱体", "6. 规划抓取路径并执行抓取", "7. 移动到目标位置: 床", "8. 精确放置并释放物体", "6. 定位白色的玻璃细长状", "7. 规划抓取路径并执行抓取", "8. 移动到目标位置: 沙发", "9. 精确放置并释放物体", "19. 验证任务完成情况", "20. 机械臂返回安全位置"], "simulation_config": {"timestep": 0.004166666666666667, "max_episode_length": 1000, "camera_resolution": [640, 480], "render_mode": "rgb_array", "physics_engine": "physx", "task_level": 4, "object_count": 4, "max_episode_steps": 1800, "success_threshold": 0.5499999999999999, "environment_config": {"location": "客厅", "objects_in_room": ["墙壁", "窗户", "椅子"], "lighting": "昏暗", "space_size": "中", "temperature": "凉爽", "noise_level": "嘈杂", "simulation_params": {"gravity": [0, 0, -9.81], "air_resistance": 0.04670171494454666, "surface_friction": 0.5119604117091148, "lighting_intensity": 0.8984080670213357, "camera_positions": [{"name": "front_view", "position": [0.8, 0.0, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "side_view", "position": [0.0, 0.8, 0.5], "target": [0.0, 0.0, 0.3], "up": [0, 0, 1]}, {"name": "top_view", "position": [0.0, 0.0, 1.2], "target": [0.0, 0.0, 0.3], "up": [1, 0, 0]}], "workspace_bounds": {"x_range": [-0.5, 0.5], "y_range": [-0.5, 0.5], "z_range": [0.0, 1.0]}}}, "robot_config": {"robot_type": "panda", "control_mode": "position", "action_space_dim": 7, "gripper_enabled": true, "max_velocity": 1.0, "max_acceleration": 2.0}, "data_collection": {"save_rgb": true, "save_depth": true, "save_segmentation": true, "save_point_cloud": true, "save_trajectory": true, "data_format": "hdf5", "compression": "gzip"}}}, "simulation_metadata": {}, "performance_metrics": {}, "trajectory_file": "demo_data/trajectories/9c706e74-2a75-4910-ad76-ba9b3388ca5b_trajectory.json"}]}