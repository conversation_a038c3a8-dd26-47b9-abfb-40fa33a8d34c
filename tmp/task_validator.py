"""
Task validation module for spatial reasoning tasks
Provides comprehensive validation for generated tasks before simulation
"""

import numpy as np
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from abc import ABC, abstractmethod
from task_generator import GeneratedTask, TaskInput
from config import TaskConfig


@dataclass
class ValidationResult:
    """
    Result of task validation with detailed feedback
    """
    is_valid: bool
    confidence_score: float
    validation_errors: List[str]
    validation_warnings: List[str]
    suggested_fixes: List[str]
    execution_feasibility: float
    spatial_consistency: float
    constraint_satisfaction: float


class BaseValidator(ABC):
    """
    Abstract base class for task validators
    """
    
    @abstractmethod
    def validate(self, task: GeneratedTask) -> ValidationResult:
        """Validate a generated task"""
        pass


class SpatialConsistencyValidator(BaseValidator):
    """
    Validates spatial consistency of task requirements
    """
    
    def __init__(self, config: TaskConfig):
        self.config = config
    
    def validate(self, task: GeneratedTask) -> ValidationResult:
        """
        Validate spatial consistency of the task
        """
        errors = []
        warnings = []
        suggestions = []
        
        spatial_score = self._check_spatial_relationships(task, errors, warnings)
        distance_score = self._check_distance_constraints(task, errors, warnings)
        placement_score = self._check_placement_feasibility(task, errors, warnings)
        
        overall_score = (spatial_score + distance_score + placement_score) / 3.0
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            confidence_score=overall_score,
            validation_errors=errors,
            validation_warnings=warnings,
            suggested_fixes=suggestions,
            execution_feasibility=placement_score,
            spatial_consistency=spatial_score,
            constraint_satisfaction=distance_score
        )
    
    def _check_spatial_relationships(self, task: GeneratedTask, errors: List[str], warnings: List[str]) -> float:
        """Check if spatial relationships are consistent"""
        score = 1.0
        spatial_reqs = task.input_data.spatial_requirements
        
        # Check for conflicting position requirements
        positions = []
        for obj_key, req in spatial_reqs.items():
            if "position" in req:
                positions.append(req["position"])
        
        # Check for impossible spatial arrangements
        if len(set(positions)) < len(positions) and task.object_count > 1:
            if task.level >= 3:  # Higher levels should have more diverse positioning
                warnings.append("Multiple objects assigned to same position - may cause conflicts")
                score -= 0.2
        
        return max(0.0, score)
    
    def _check_distance_constraints(self, task: GeneratedTask, errors: List[str], warnings: List[str]) -> float:
        """Check if distance constraints are achievable"""
        score = 1.0
        spatial_reqs = task.input_data.spatial_requirements
        
        for obj_key, req in spatial_reqs.items():
            if "distance_constraint" in req:
                distance_str = req["distance_constraint"]
                try:
                    distance = float(distance_str.replace("厘米", ""))
                    if distance < 1 or distance > 100:  # Reasonable range check
                        errors.append(f"Unrealistic distance constraint: {distance_str}")
                        score -= 0.3
                except ValueError:
                    errors.append(f"Invalid distance format: {distance_str}")
                    score -= 0.5
        
        return max(0.0, score)
    
    def _check_placement_feasibility(self, task: GeneratedTask, errors: List[str], warnings: List[str]) -> float:
        """Check if object placement is physically feasible"""
        score = 1.0
        obj_attrs = task.input_data.object_attributes
        spatial_reqs = task.input_data.spatial_requirements
        
        for obj_key in obj_attrs.keys():
            attrs = obj_attrs[obj_key]
            spatial_req = spatial_reqs.get(obj_key, {})
            
            # Check material-position compatibility
            material = attrs.get("material", "")
            position = spatial_req.get("position", "")
            
            if material == "玻璃" and position in ["地板"]:
                warnings.append(f"Glass object on floor may be fragile - consider safer placement")
                score -= 0.1
            
            if attrs.get("weight") == "重" and position in ["书架", "窗台"]:
                warnings.append(f"Heavy object on {position} may not be stable")
                score -= 0.1
        
        return max(0.0, score)


class PhysicsValidator(BaseValidator):
    """
    Validates physics constraints and feasibility
    """
    
    def __init__(self, config: TaskConfig):
        self.config = config
    
    def validate(self, task: GeneratedTask) -> ValidationResult:
        """
        Validate physics constraints of the task
        """
        errors = []
        warnings = []
        suggestions = []
        
        stability_score = self._check_stability_requirements(task, errors, warnings)
        collision_score = self._check_collision_avoidance(task, errors, warnings)
        grasp_score = self._check_graspability(task, errors, warnings)
        
        overall_score = (stability_score + collision_score + grasp_score) / 3.0
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            confidence_score=overall_score,
            validation_errors=errors,
            validation_warnings=warnings,
            suggested_fixes=suggestions,
            execution_feasibility=overall_score,
            spatial_consistency=stability_score,
            constraint_satisfaction=collision_score
        )
    
    def _check_stability_requirements(self, task: GeneratedTask, errors: List[str], warnings: List[str]) -> float:
        """Check if stability requirements are realistic"""
        score = 1.0
        constraints = task.input_data.constraints
        
        if "stability" in constraints:
            stability_req = constraints["stability"]
            if "必须稳定" in stability_req and task.object_count > 3:
                warnings.append("High stability requirement with many objects may be challenging")
                score -= 0.1
        
        return score
    
    def _check_collision_avoidance(self, task: GeneratedTask, errors: List[str], warnings: List[str]) -> float:
        """Check collision avoidance feasibility"""
        score = 1.0
        
        if task.object_count > 2 and task.level >= 3:
            # Check if workspace is sufficient for multiple objects
            workspace_bounds = task.simulation_config.get("environment_config", {}).get("simulation_params", {}).get("workspace_bounds", {})
            if workspace_bounds:
                x_range = workspace_bounds.get("x_range", [-0.5, 0.5])
                y_range = workspace_bounds.get("y_range", [-0.5, 0.5])
                workspace_area = (x_range[1] - x_range[0]) * (y_range[1] - y_range[0])
                
                if workspace_area < task.object_count * 0.1:  # Rough estimate
                    warnings.append("Workspace may be too small for all objects")
                    score -= 0.2
        
        return score
    
    def _check_graspability(self, task: GeneratedTask, errors: List[str], warnings: List[str]) -> float:
        """Check if objects are graspable"""
        score = 1.0
        obj_attrs = task.input_data.object_attributes
        
        for obj_key, attrs in obj_attrs.items():
            shape = attrs.get("shape", "")
            material = attrs.get("material", "")
            size = attrs.get("size", "")
            
            # Check difficult grasping scenarios
            if shape == "球体" and material == "玻璃":
                warnings.append(f"Glass sphere may be difficult to grasp securely")
                score -= 0.1
            
            if size == "小" and material == "金属":
                warnings.append(f"Small metal objects may slip during grasping")
                score -= 0.1
        
        return max(0.0, score)


class ComprehensiveTaskValidator:
    """
    Main validator that combines multiple validation strategies
    """
    
    def __init__(self, config: TaskConfig = None):
        self.config = config or TaskConfig()
        self.validators = [
            SpatialConsistencyValidator(self.config),
            PhysicsValidator(self.config)
        ]
    
    def validate_task(self, task: GeneratedTask) -> ValidationResult:
        """
        Perform comprehensive validation of a generated task
        
        Args:
            task: Generated task to validate
            
        Returns:
            ValidationResult: Comprehensive validation result
        """
        all_errors = []
        all_warnings = []
        all_suggestions = []
        scores = []
        
        # Run all validators
        for validator in self.validators:
            result = validator.validate(task)
            all_errors.extend(result.validation_errors)
            all_warnings.extend(result.validation_warnings)
            all_suggestions.extend(result.suggested_fixes)
            scores.append(result.confidence_score)
        
        # Calculate overall scores
        avg_confidence = sum(scores) / len(scores) if scores else 0.0
        
        # Additional high-level checks
        self._check_task_completeness(task, all_errors, all_warnings)
        self._check_difficulty_appropriateness(task, all_warnings)
        
        return ValidationResult(
            is_valid=len(all_errors) == 0,
            confidence_score=avg_confidence,
            validation_errors=list(set(all_errors)),  # Remove duplicates
            validation_warnings=list(set(all_warnings)),
            suggested_fixes=list(set(all_suggestions)),
            execution_feasibility=avg_confidence,
            spatial_consistency=scores[0] if len(scores) > 0 else 0.0,
            constraint_satisfaction=scores[1] if len(scores) > 1 else 0.0
        )
    
    def _check_task_completeness(self, task: GeneratedTask, errors: List[str], warnings: List[str]) -> None:
        """Check if task has all required components"""
        if not task.description or len(task.description.strip()) < 10:
            errors.append("Task description is too short or missing")
        
        if not task.success_criteria or len(task.success_criteria) < 2:
            errors.append("Insufficient success criteria defined")
        
        if not task.execution_steps or len(task.execution_steps) < 4:
            errors.append("Insufficient execution steps defined")
    
    def _check_difficulty_appropriateness(self, task: GeneratedTask, warnings: List[str]) -> None:
        """Check if task difficulty matches its level"""
        expected_complexity = self.config.DIFFICULTY_LEVELS[task.level]["complexity_score"]
        
        if task.complexity_score < expected_complexity * 0.7:
            warnings.append(f"Task may be too simple for level {task.level}")
        elif task.complexity_score > expected_complexity * 1.5:
            warnings.append(f"Task may be too complex for level {task.level}")
    
    def validate_batch(self, tasks: List[GeneratedTask]) -> Dict[str, Any]:
        """
        Validate a batch of tasks and provide summary statistics
        
        Args:
            tasks: List of tasks to validate
            
        Returns:
            Dict containing validation summary and individual results
        """
        results = []
        valid_count = 0
        total_confidence = 0.0
        
        for task in tasks:
            result = self.validate_task(task)
            results.append({
                "task_id": task.task_id,
                "validation_result": result
            })
            
            if result.is_valid:
                valid_count += 1
            total_confidence += result.confidence_score
        
        avg_confidence = total_confidence / len(tasks) if tasks else 0.0
        
        return {
            "summary": {
                "total_tasks": len(tasks),
                "valid_tasks": valid_count,
                "validation_rate": valid_count / len(tasks) if tasks else 0.0,
                "average_confidence": avg_confidence
            },
            "individual_results": results
        }
