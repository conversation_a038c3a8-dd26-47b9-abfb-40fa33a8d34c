"""
Core task generation module for spatial reasoning tasks
Enhanced with better extensibility and SAPIEN integration support
"""

import json
import random
import uuid
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
from config import TaskConfig

@dataclass
class TaskInput:
    """
    Enhanced input structure for task generation with additional metadata
    """
    level: int
    spatial_requirements: Dict[str, Any]
    object_attributes: Dict[str, Any]
    constraints: Dict[str, Any]
    environment_context: Dict[str, Any]
    task_id: str = ""
    timestamp: str = ""
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if not self.task_id:
            self.task_id = str(uuid.uuid4())
        if self.metadata is None:
            self.metadata = {}


@dataclass
class GeneratedTask:
    """
    Structure for generated task with comprehensive metadata
    """
    task_id: str
    level: int
    description: str
    input_data: TaskInput
    complexity_score: float
    estimated_difficulty: str
    spatial_constraints: List[str]
    object_count: int
    success_criteria: List[str]
    execution_steps: List[str]
    simulation_config: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return asdict(self)


class BaseTaskGenerator(ABC):
    """
    Abstract base class for task generators to ensure extensibility
    """

    @abstractmethod
    def generate_task_input(self, level: int, **kwargs) -> TaskInput:
        """Generate structured task input"""
        pass

    @abstractmethod
    def generate_task_description(self, task_input: TaskInput) -> str:
        """Generate natural language task description"""
        pass

    @abstractmethod
    def validate_task(self, task: GeneratedTask) -> bool:
        """Validate if task is executable"""
        pass

class SpatialReasoningTaskGenerator(BaseTaskGenerator):
    """
    Enhanced main task generator class with improved extensibility and SAPIEN integration
    """

    def __init__(self, config: TaskConfig = None):
        self.config = config or TaskConfig()
        self.task_history: List[GeneratedTask] = []
        self.generation_stats = {
            "total_generated": 0,
            "level_distribution": {1: 0, 2: 0, 3: 0, 4: 0},
            "success_rate": 0.0
        }
        
    def generate_task_input(self, level: int, num_objects: int = 1, **kwargs) -> TaskInput:
        """
        Enhanced task input generation with additional parameters

        Args:
            level: Task complexity level (1-4)
            num_objects: Number of objects in the task
            **kwargs: Additional parameters for customization

        Returns:
            TaskInput: Enhanced structured input for task generation
        """
        # Validate level
        if level not in [1, 2, 3, 4]:
            raise ValueError(f"Invalid task level: {level}. Must be 1-4.")

        # Adjust object count based on level constraints
        max_objects = self.config.DIFFICULTY_LEVELS[level]["max_objects"]
        num_objects = min(num_objects, max_objects)

        spatial_reqs = self._generate_spatial_requirements(level, num_objects, **kwargs)
        obj_attrs = self._generate_object_attributes(num_objects, **kwargs)
        constraints = self._generate_constraints(level, **kwargs)
        env_context = self._generate_environment_context(**kwargs)

        task_input = TaskInput(
            level=level,
            spatial_requirements=spatial_reqs,
            object_attributes=obj_attrs,
            constraints=constraints,
            environment_context=env_context
        )

        # Add metadata
        task_input.metadata.update({
            "num_objects": num_objects,
            "generation_method": "automatic",
            "custom_params": kwargs
        })

        return task_input
    
    def _generate_spatial_requirements(self, level: int, num_objects: int, **kwargs) -> Dict[str, Any]:
        """
        Enhanced spatial requirements generation with customization support
        """
        requirements = {}
        custom_locations = kwargs.get("custom_locations", self.config.LOCATIONS)
        custom_positions = kwargs.get("custom_positions", self.config.RELATIVE_POSITIONS)

        for i in range(num_objects):
            obj_key = f"object_{i+1}"
            requirements[obj_key] = {
                "position": random.choice(custom_locations),
                "relative_position": random.choice(custom_positions),
                "object_id": f"obj_{i+1}_{uuid.uuid4().hex[:8]}"
            }

            if level >= 2:
                requirements[obj_key]["orientation"] = random.choice(["水平", "垂直", "倾斜"])

            if level >= 3:
                requirements[obj_key]["distance_constraint"] = f"{random.choice(self.config.DISTANCE_CONSTRAINTS)}厘米"
                requirements[obj_key]["precision_level"] = random.choice(["粗略", "精确", "高精度"])

            if level >= 4 and i > 0:
                requirements[obj_key]["reference_object"] = f"object_{i}"
                requirements[obj_key]["spatial_relationship"] = random.choice(self.config.RELATIONSHIPS)

        return requirements
    
    def _generate_object_attributes(self, num_objects: int, **kwargs) -> Dict[str, Any]:
        """
        Enhanced object attributes generation with physical properties
        """
        attributes = {}
        custom_materials = kwargs.get("custom_materials", self.config.MATERIALS)
        custom_colors = kwargs.get("custom_colors", self.config.COLORS)
        custom_shapes = kwargs.get("custom_shapes", self.config.SHAPES)

        for i in range(num_objects):
            obj_key = f"object_{i+1}"
            attributes[obj_key] = {
                "material": random.choice(custom_materials),
                "color": random.choice(custom_colors),
                "shape": random.choice(custom_shapes),
                "size": random.choice(["小", "中", "大"]),
                "weight": random.choice(["轻", "中等", "重"]),
                "texture": random.choice(["光滑", "粗糙", "柔软", "坚硬"]),
                "transparency": random.choice(["透明", "半透明", "不透明"]),
                "graspability": random.uniform(0.3, 1.0)  # For simulation
            }

        return attributes
    
    def _generate_constraints(self, level: int, **kwargs) -> Dict[str, Any]:
        """
        Enhanced constraint generation with simulation-ready parameters
        """
        constraints = {}
        max_constraints = self.config.DIFFICULTY_LEVELS[level]["max_constraints"]

        if level >= 1:
            constraints["stability"] = random.choice(["必须稳定", "允许轻微摆动"])

        if level >= 2:
            constraints["alignment"] = random.choice(["水平", "垂直", "对角"])
            constraints["collision_avoidance"] = True

        if level >= 3:
            constraints["distance"] = f"{random.choice(self.config.DISTANCE_CONSTRAINTS)}厘米"
            constraints["relationship"] = random.choice(self.config.RELATIONSHIPS)
            constraints["precision_tolerance"] = random.uniform(0.01, 0.05)  # meters

        if level >= 4:
            constraints["complex_constraint"] = random.choice(self.config.COMPLEX_CONSTRAINTS)
            constraints["multi_object_coordination"] = True
            constraints["temporal_constraints"] = random.choice(["同时执行", "顺序执行", "条件执行"])

        # Add simulation-specific constraints
        constraints["physics_enabled"] = True
        constraints["max_execution_time"] = 30.0 + level * 10.0  # seconds

        return constraints
    
    def _generate_environment_context(self, **kwargs) -> Dict[str, Any]:
        """
        Enhanced environment context generation with simulation parameters
        """
        custom_environments = kwargs.get("custom_environments", self.config.ENVIRONMENTS)

        context = {
            "location": random.choice(custom_environments),
            "objects_in_room": random.sample(self.config.LOCATIONS, k=min(3, len(self.config.LOCATIONS))),
            "lighting": random.choice(["明亮", "昏暗", "自然光"]),
            "space_size": random.choice(["小", "中", "大"]),
            "temperature": random.choice(["常温", "温暖", "凉爽"]),
            "noise_level": random.choice(["安静", "轻微噪音", "嘈杂"])
        }

        # Add simulation-specific environment parameters
        context["simulation_params"] = {
            "gravity": [0, 0, -9.81],
            "air_resistance": random.uniform(0.0, 0.1),
            "surface_friction": random.uniform(0.3, 0.8),
            "lighting_intensity": random.uniform(0.5, 1.0),
            "camera_positions": self._generate_camera_positions(),
            "workspace_bounds": {
                "x_range": [-0.5, 0.5],
                "y_range": [-0.5, 0.5],
                "z_range": [0.0, 1.0]
            }
        }

        return context

    def _generate_camera_positions(self) -> List[Dict[str, Any]]:
        """Generate camera positions for simulation"""
        positions = []
        # Front view
        positions.append({
            "name": "front_view",
            "position": [0.8, 0.0, 0.5],
            "target": [0.0, 0.0, 0.3],
            "up": [0, 0, 1]
        })
        # Side view
        positions.append({
            "name": "side_view",
            "position": [0.0, 0.8, 0.5],
            "target": [0.0, 0.0, 0.3],
            "up": [0, 0, 1]
        })
        # Top view
        positions.append({
            "name": "top_view",
            "position": [0.0, 0.0, 1.2],
            "target": [0.0, 0.0, 0.3],
            "up": [1, 0, 0]
        })
        return positions
    
    def generate_task_description(self, task_input: TaskInput) -> str:
        """
        Generate natural language task description from structured input

        Args:
            task_input: Structured task input

        Returns:
            str: Natural language task description
        """
        level = task_input.level
        templates = self.config.TASK_TEMPLATES[level]
        template = random.choice(templates)

        if level == 1:
            return self._generate_level1_task(task_input, template)
        elif level == 2:
            return self._generate_level2_task(task_input, template)
        elif level == 3:
            return self._generate_level3_task(task_input, template)
        elif level == 4:
            return self._generate_level4_task(task_input, template)

        return ""

    def generate_complete_task(self, task_input: TaskInput) -> GeneratedTask:
        """
        Generate a complete task with all metadata and simulation parameters

        Args:
            task_input: Structured task input

        Returns:
            GeneratedTask: Complete task with metadata
        """
        description = self.generate_task_description(task_input)
        complexity_score = self._calculate_complexity_score(task_input)
        estimated_difficulty = self._estimate_difficulty(task_input.level,
                                                       len(task_input.object_attributes))

        # Generate additional task components
        spatial_constraints = self._extract_spatial_constraints(task_input)
        success_criteria = self._generate_success_criteria(task_input)
        execution_steps = self._generate_execution_steps(task_input)
        simulation_config = self._generate_simulation_config(task_input)

        task = GeneratedTask(
            task_id=task_input.task_id,
            level=task_input.level,
            description=description,
            input_data=task_input,
            complexity_score=complexity_score,
            estimated_difficulty=estimated_difficulty,
            spatial_constraints=spatial_constraints,
            object_count=len(task_input.object_attributes),
            success_criteria=success_criteria,
            execution_steps=execution_steps,
            simulation_config=simulation_config
        )

        # Update statistics
        self.generation_stats["total_generated"] += 1
        self.generation_stats["level_distribution"][task_input.level] += 1

        return task

    def _extract_spatial_constraints(self, task_input: TaskInput) -> List[str]:
        """Extract spatial constraints from task input"""
        constraints = []

        for obj_key, spatial_req in task_input.spatial_requirements.items():
            if "distance_constraint" in spatial_req:
                constraints.append(f"{obj_key}: {spatial_req['distance_constraint']}")
            if "orientation" in spatial_req:
                constraints.append(f"{obj_key}: {spatial_req['orientation']}")

        for constraint_key, constraint_value in task_input.constraints.items():
            if isinstance(constraint_value, str):
                constraints.append(f"{constraint_key}: {constraint_value}")

        return constraints

    def _generate_success_criteria(self, task_input: TaskInput) -> List[str]:
        """Generate success criteria for task validation"""
        criteria = []
        level = task_input.level

        # Basic criteria for all levels
        criteria.append("所有物体必须成功抓取和放置")
        criteria.append("任务执行过程中无碰撞")

        if level >= 2:
            criteria.append("物体属性匹配要求")
            criteria.append("相对位置关系正确")

        if level >= 3:
            criteria.append("距离约束满足")
            criteria.append("空间关系准确")

        if level >= 4:
            criteria.append("多物体协调完成")
            criteria.append("复杂约束全部满足")

        return criteria

    def _generate_execution_steps(self, task_input: TaskInput) -> List[str]:
        """Generate execution steps for the task"""
        steps = []
        objects = list(task_input.object_attributes.keys())

        steps.append("1. 初始化机械臂到准备位置")
        steps.append("2. 扫描工作空间，识别目标物体")

        for i, obj_key in enumerate(objects):
            obj_attrs = task_input.object_attributes[obj_key]
            spatial_req = task_input.spatial_requirements[obj_key]

            steps.append(f"{i+3}. 定位{obj_attrs['color']}的{obj_attrs['material']}{obj_attrs['shape']}")
            steps.append(f"{i+4}. 规划抓取路径并执行抓取")
            steps.append(f"{i+5}. 移动到目标位置: {spatial_req['position']}")
            steps.append(f"{i+6}. 精确放置并释放物体")

        steps.append(f"{len(steps)+1}. 验证任务完成情况")
        steps.append(f"{len(steps)+1}. 机械臂返回安全位置")

        return steps

    def _generate_simulation_config(self, task_input: TaskInput) -> Dict[str, Any]:
        """Generate simulation configuration for SAPIEN"""
        base_config = self.config.SIMULATION_CONFIG.copy()

        # Adjust based on task complexity
        base_config.update({
            "task_level": task_input.level,
            "object_count": len(task_input.object_attributes),
            "max_episode_steps": base_config["max_episode_length"] + task_input.level * 200,
            "success_threshold": 0.95 - task_input.level * 0.1,
            "environment_config": task_input.environment_context,
            "robot_config": self.config.ROBOT_CONFIG.copy(),
            "data_collection": self.config.DATA_COLLECTION_CONFIG.copy()
        })

        return base_config

    def validate_task(self, task: GeneratedTask) -> bool:
        """
        Validate if a generated task is executable and well-formed

        Args:
            task: Generated task to validate

        Returns:
            bool: True if task is valid, False otherwise
        """
        try:
            # Check basic structure
            if not task.task_id or not task.description:
                return False

            # Check level validity
            if task.level not in [1, 2, 3, 4]:
                return False

            # Check object count constraints
            max_objects = self.config.DIFFICULTY_LEVELS[task.level]["max_objects"]
            if task.object_count > max_objects:
                return False

            # Check spatial requirements
            spatial_reqs = task.input_data.spatial_requirements
            if not spatial_reqs or len(spatial_reqs) != task.object_count:
                return False

            # Check object attributes
            obj_attrs = task.input_data.object_attributes
            if not obj_attrs or len(obj_attrs) != task.object_count:
                return False

            # Validate each object has required attributes
            required_attrs = ["material", "color", "shape"]
            for obj_key, attrs in obj_attrs.items():
                if not all(attr in attrs for attr in required_attrs):
                    return False

            # Check success criteria
            if not task.success_criteria or len(task.success_criteria) < 2:
                return False

            # Check execution steps
            if not task.execution_steps or len(task.execution_steps) < 4:
                return False

            return True

        except Exception as e:
            print(f"Task validation error: {e}")
            return False
    
    def _generate_level1_task(self, task_input: TaskInput, template: str) -> str:
        """
        Generate Level 1 task description focusing on basic spatial positioning
        """
        spatial_req = list(task_input.spatial_requirements.values())[0]
        
        return template.format(
            location=spatial_req["position"],
            relative_position=spatial_req.get("relative_position", ""),
            location1=spatial_req["position"],
            location2=random.choice(self.config.LOCATIONS)
        )
    
    def _generate_level2_task(self, task_input: TaskInput, template: str) -> str:
        """
        Generate Level 2 task description combining position and object attributes
        """
        spatial_req = list(task_input.spatial_requirements.values())[0]
        obj_attr = list(task_input.object_attributes.values())[0]
        
        return template.format(
            location=spatial_req["position"],
            relative_position=spatial_req.get("relative_position", ""),
            material=obj_attr["material"],
            color=obj_attr["color"],
            shape=obj_attr["shape"]
        )
    
    def _generate_level3_task(self, task_input: TaskInput, template: str) -> str:
        """
        Generate Level 3 task description with complex spatial relationships
        """
        spatial_req = list(task_input.spatial_requirements.values())[0]
        obj_attr = list(task_input.object_attributes.values())[0]
        
        return template.format(
            location=spatial_req["position"],
            relative_position=spatial_req.get("relative_position", ""),
            reference_object=f"{obj_attr['color']}的{obj_attr['material']}物体",
            distance=random.choice(self.config.DISTANCE_CONSTRAINTS)
        )
    
    def _generate_level4_task(self, task_input: TaskInput, template: str) -> str:
        """
        Generate Level 4 task description with multi-object spatial reasoning
        """
        spatial_reqs = list(task_input.spatial_requirements.values())
        
        location1 = spatial_reqs[0]["position"] if len(spatial_reqs) > 0 else random.choice(self.config.LOCATIONS)
        location2 = spatial_reqs[1]["position"] if len(spatial_reqs) > 1 else random.choice(self.config.LOCATIONS)
        
        return template.format(
            location1=location1,
            location2=location2,
            constraint=random.choice(self.config.RELATIONSHIPS),
            relationship=random.choice(self.config.RELATIONSHIPS),
            complex_constraint=random.choice(self.config.COMPLEX_CONSTRAINTS)
        )
    
    def generate_batch_tasks(self, num_tasks: int, level_distribution: Dict[int, float] = None,
                           validate_tasks: bool = True) -> List[GeneratedTask]:
        """
        Enhanced batch task generation with validation and better distribution control

        Args:
            num_tasks: Total number of tasks to generate
            level_distribution: Distribution of tasks across levels (probabilities)
            validate_tasks: Whether to validate generated tasks

        Returns:
            List[GeneratedTask]: List of validated generated tasks
        """
        if level_distribution is None:
            level_distribution = {1: 0.25, 2: 0.25, 3: 0.25, 4: 0.25}

        tasks = []
        failed_generations = 0
        max_retries = num_tasks * 2  # Allow some retries for failed generations

        while len(tasks) < num_tasks and failed_generations < max_retries:
            try:
                # Select level based on distribution
                level = random.choices(
                    list(level_distribution.keys()),
                    weights=list(level_distribution.values())
                )[0]

                # Determine object count based on level
                max_objects = self.config.DIFFICULTY_LEVELS[level]["max_objects"]
                if level <= 2:
                    num_objects = random.randint(1, min(2, max_objects))
                else:
                    num_objects = random.randint(1, max_objects)

                # Generate task
                task_input = self.generate_task_input(level, num_objects)
                task = self.generate_complete_task(task_input)

                # Validate if requested
                if validate_tasks:
                    if not self.validate_task(task):
                        failed_generations += 1
                        continue

                tasks.append(task)
                self.task_history.append(task)

            except Exception as e:
                print(f"Error generating task: {e}")
                failed_generations += 1
                continue

        # Update success rate
        total_attempts = len(tasks) + failed_generations
        self.generation_stats["success_rate"] = len(tasks) / total_attempts if total_attempts > 0 else 0.0

        return tasks

    def save_tasks_to_file(self, tasks: List[GeneratedTask], filepath: str) -> None:
        """Save generated tasks to JSON file"""
        task_data = {
            "metadata": {
                "total_tasks": len(tasks),
                "generation_stats": self.generation_stats,
                "config_version": "1.0"
            },
            "tasks": [task.to_dict() for task in tasks]
        }

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(task_data, f, ensure_ascii=False, indent=2)

    def load_tasks_from_file(self, filepath: str) -> List[GeneratedTask]:
        """Load tasks from JSON file"""
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)

        tasks = []
        for task_dict in data.get("tasks", []):
            # Reconstruct TaskInput
            input_data_dict = task_dict["input_data"]
            task_input = TaskInput(**input_data_dict)

            # Reconstruct GeneratedTask
            task_dict["input_data"] = task_input
            task = GeneratedTask(**task_dict)
            tasks.append(task)

        return tasks

    def get_generation_statistics(self) -> Dict[str, Any]:
        """Get detailed generation statistics"""
        return {
            "total_generated": self.generation_stats["total_generated"],
            "level_distribution": self.generation_stats["level_distribution"],
            "success_rate": self.generation_stats["success_rate"],
            "tasks_in_history": len(self.task_history),
            "average_complexity": sum(task.complexity_score for task in self.task_history) / len(self.task_history) if self.task_history else 0.0
        }

    def filter_tasks_by_criteria(self, tasks: List[GeneratedTask],
                                level: int = None,
                                min_complexity: float = None,
                                max_complexity: float = None,
                                object_count: int = None) -> List[GeneratedTask]:
        """Filter tasks based on specified criteria"""
        filtered_tasks = tasks

        if level is not None:
            filtered_tasks = [task for task in filtered_tasks if task.level == level]

        if min_complexity is not None:
            filtered_tasks = [task for task in filtered_tasks if task.complexity_score >= min_complexity]

        if max_complexity is not None:
            filtered_tasks = [task for task in filtered_tasks if task.complexity_score <= max_complexity]

        if object_count is not None:
            filtered_tasks = [task for task in filtered_tasks if task.object_count == object_count]

        return filtered_tasks
    
    def _calculate_complexity_score(self, task_input: TaskInput) -> float:
        """
        Calculate complexity score based on task characteristics
        """
        score = task_input.level * 2
        score += len(task_input.object_attributes) * 1.5
        score += len(task_input.constraints) * 1.2
        return round(score, 2)
    
    def _estimate_difficulty(self, level: int, num_objects: int) -> str:
        """
        Estimate task difficulty based on level and object count
        """
        if level == 1:
            return "简单"
        elif level == 2:
            return "中等"
        elif level == 3:
            return "困难"
        else:
            return "非常困难" if num_objects > 2 else "困难" 