"""
Core task generation module for spatial reasoning tasks
"""

import json
import random
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from config import TaskConfig

@dataclass
class TaskInput:
    """
    Input structure for task generation containing level, spatial requirements, 
    object attributes, constraints and environment context
    """
    level: int
    spatial_requirements: Dict[str, Any]
    object_attributes: Dict[str, Any]
    constraints: Dict[str, Any]
    environment_context: Dict[str, Any]

class SpatialReasoningTaskGenerator:
    """
    Main task generator class that creates spatial reasoning tasks based on 
    different complexity levels and input parameters
    """
    
    def __init__(self, config: TaskConfig = None):
        self.config = config or TaskConfig()
        self.task_history = []
        
    def generate_task_input(self, level: int, num_objects: int = 1) -> TaskInput:
        """
        Generate structured task input based on specified level and object count
        
        Args:
            level: Task complexity level (1-4)
            num_objects: Number of objects in the task
            
        Returns:
            TaskInput: Structured input for task generation
        """
        spatial_reqs = self._generate_spatial_requirements(level, num_objects)
        obj_attrs = self._generate_object_attributes(num_objects)
        constraints = self._generate_constraints(level)
        env_context = self._generate_environment_context()
        
        return TaskInput(
            level=level,
            spatial_requirements=spatial_reqs,
            object_attributes=obj_attrs,
            constraints=constraints,
            environment_context=env_context
        )
    
    def _generate_spatial_requirements(self, level: int, num_objects: int) -> Dict[str, Any]:
        """
        Generate spatial requirements based on task level and object count
        """
        requirements = {}
        
        for i in range(num_objects):
            obj_key = f"object_{i+1}"
            requirements[obj_key] = {
                "position": random.choice(self.config.LOCATIONS),
                "relative_position": random.choice(self.config.RELATIVE_POSITIONS)
            }
            
            if level >= 3:
                requirements[obj_key]["distance_constraint"] = f"{random.choice(self.config.DISTANCE_CONSTRAINTS)}厘米"
            
            if level >= 4 and i > 0:
                requirements[obj_key]["reference_object"] = f"object_{i}"
        
        return requirements
    
    def _generate_object_attributes(self, num_objects: int) -> Dict[str, Any]:
        """
        Generate object attributes including material, color, and shape
        """
        attributes = {}
        
        for i in range(num_objects):
            obj_key = f"object_{i+1}"
            attributes[obj_key] = {
                "material": random.choice(self.config.MATERIALS),
                "color": random.choice(self.config.COLORS),
                "shape": random.choice(self.config.SHAPES)
            }
        
        return attributes
    
    def _generate_constraints(self, level: int) -> Dict[str, Any]:
        """
        Generate task constraints based on complexity level
        """
        constraints = {}
        
        if level >= 2:
            constraints["alignment"] = random.choice(["水平", "垂直", "对角"])
        
        if level >= 3:
            constraints["distance"] = f"{random.choice(self.config.DISTANCE_CONSTRAINTS)}厘米"
            constraints["relationship"] = random.choice(self.config.RELATIONSHIPS)
        
        if level >= 4:
            constraints["complex_constraint"] = random.choice(self.config.COMPLEX_CONSTRAINTS)
        
        return constraints
    
    def _generate_environment_context(self) -> Dict[str, Any]:
        """
        Generate environment context including location and surrounding objects
        """
        return {
            "location": random.choice(["厨房", "客厅", "书房", "卧室"]),
            "objects_in_room": random.sample(self.config.LOCATIONS, k=3),
            "lighting": random.choice(["明亮", "昏暗", "自然光"]),
            "space_size": random.choice(["小", "中", "大"])
        }
    
    def generate_task_description(self, task_input: TaskInput) -> str:
        """
        Generate natural language task description from structured input
        
        Args:
            task_input: Structured task input
            
        Returns:
            str: Natural language task description
        """
        level = task_input.level
        templates = self.config.TASK_TEMPLATES[level]
        template = random.choice(templates)
        
        if level == 1:
            return self._generate_level1_task(task_input, template)
        elif level == 2:
            return self._generate_level2_task(task_input, template)
        elif level == 3:
            return self._generate_level3_task(task_input, template)
        elif level == 4:
            return self._generate_level4_task(task_input, template)
        
        return ""
    
    def _generate_level1_task(self, task_input: TaskInput, template: str) -> str:
        """
        Generate Level 1 task description focusing on basic spatial positioning
        """
        spatial_req = list(task_input.spatial_requirements.values())[0]
        
        return template.format(
            location=spatial_req["position"],
            relative_position=spatial_req.get("relative_position", ""),
            location1=spatial_req["position"],
            location2=random.choice(self.config.LOCATIONS)
        )
    
    def _generate_level2_task(self, task_input: TaskInput, template: str) -> str:
        """
        Generate Level 2 task description combining position and object attributes
        """
        spatial_req = list(task_input.spatial_requirements.values())[0]
        obj_attr = list(task_input.object_attributes.values())[0]
        
        return template.format(
            location=spatial_req["position"],
            relative_position=spatial_req.get("relative_position", ""),
            material=obj_attr["material"],
            color=obj_attr["color"],
            shape=obj_attr["shape"]
        )
    
    def _generate_level3_task(self, task_input: TaskInput, template: str) -> str:
        """
        Generate Level 3 task description with complex spatial relationships
        """
        spatial_req = list(task_input.spatial_requirements.values())[0]
        obj_attr = list(task_input.object_attributes.values())[0]
        
        return template.format(
            location=spatial_req["position"],
            relative_position=spatial_req.get("relative_position", ""),
            reference_object=f"{obj_attr['color']}的{obj_attr['material']}物体",
            distance=random.choice(self.config.DISTANCE_CONSTRAINTS)
        )
    
    def _generate_level4_task(self, task_input: TaskInput, template: str) -> str:
        """
        Generate Level 4 task description with multi-object spatial reasoning
        """
        spatial_reqs = list(task_input.spatial_requirements.values())
        
        location1 = spatial_reqs[0]["position"] if len(spatial_reqs) > 0 else random.choice(self.config.LOCATIONS)
        location2 = spatial_reqs[1]["position"] if len(spatial_reqs) > 1 else random.choice(self.config.LOCATIONS)
        
        return template.format(
            location1=location1,
            location2=location2,
            constraint=random.choice(self.config.RELATIONSHIPS),
            relationship=random.choice(self.config.RELATIONSHIPS),
            complex_constraint=random.choice(self.config.COMPLEX_CONSTRAINTS)
        )
    
    def generate_batch_tasks(self, num_tasks: int, level_distribution: Dict[int, int] = None) -> List[Dict[str, Any]]:
        """
        Generate a batch of tasks with specified level distribution
        
        Args:
            num_tasks: Total number of tasks to generate
            level_distribution: Distribution of tasks across levels
            
        Returns:
            List[Dict]: List of generated tasks with metadata
        """
        if level_distribution is None:
            level_distribution = {1: 0.25, 2: 0.25, 3: 0.25, 4: 0.25}
        
        tasks = []
        
        for _ in range(num_tasks):
            level = random.choices(
                list(level_distribution.keys()),
                weights=list(level_distribution.values())
            )[0]
            
            num_objects = 1 if level <= 2 else random.randint(1, 3)
            task_input = self.generate_task_input(level, num_objects)
            task_description = self.generate_task_description(task_input)
            
            task = {
                "task_id": f"task_{len(tasks)+1:04d}",
                "level": level,
                "description": task_description,
                "input_data": task_input.__dict__,
                "complexity_score": self._calculate_complexity_score(task_input),
                "estimated_difficulty": self._estimate_difficulty(level, num_objects)
            }
            
            tasks.append(task)
        
        return tasks
    
    def _calculate_complexity_score(self, task_input: TaskInput) -> float:
        """
        Calculate complexity score based on task characteristics
        """
        score = task_input.level * 2
        score += len(task_input.object_attributes) * 1.5
        score += len(task_input.constraints) * 1.2
        return round(score, 2)
    
    def _estimate_difficulty(self, level: int, num_objects: int) -> str:
        """
        Estimate task difficulty based on level and object count
        """
        if level == 1:
            return "简单"
        elif level == 2:
            return "中等"
        elif level == 3:
            return "困难"
        else:
            return "非常困难" if num_objects > 2 else "困难" 