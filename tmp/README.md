# 基于LLM的空间推理任务生成框架

这是一个基于大语言模型(LLM)的自动任务生成框架，专门用于生成适用于机械臂操控任务的空间推理任务。框架支持从简单的物体抓取任务逐渐递进到复杂的空间推理任务，并集成了SAPIEN仿真器进行仿真数据的生成和收集。

## 🎯 主要特性

- **多层次任务生成**: 支持Level 1-4的任务复杂度，从基础空间定位到复杂多物体协调
- **LLM智能增强**: 集成大语言模型进行更自然、更具创造性的任务描述生成
- **全面任务验证**: 多维度验证任务的可执行性、空间一致性和物理合理性
- **SAPIEN仿真集成**: 完整的仿真环境支持，包括物理引擎、机器人控制和数据收集
- **可扩展架构**: 模块化设计，支持自定义任务生成器、验证器和仿真环境
- **批量数据收集**: 自动化的数据收集管道，支持并行执行和大规模数据生成

## 📁 项目结构

```
tmp/
├── config.py                    # 配置文件，定义所有参数
├── task_generator.py           # 核心任务生成器
├── llm_integration.py          # LLM集成模块
├── task_validator.py           # 任务验证器
├── sapien_integration.py       # SAPIEN仿真器集成
├── data_collection_pipeline.py # 数据收集管道
├── examples_and_tests.py       # 示例和测试
└── README.md                   # 使用说明
```

## 🚀 快速开始

### 1. 基础任务生成

```python
from config import TaskConfig
from task_generator import SpatialReasoningTaskGenerator

# 初始化配置和生成器
config = TaskConfig()
generator = SpatialReasoningTaskGenerator(config)

# 生成单个任务
task_input = generator.generate_task_input(level=2, num_objects=1)
task = generator.generate_complete_task(task_input)

print(f"任务描述: {task.description}")
print(f"复杂度评分: {task.complexity_score}")
```

### 2. LLM增强任务生成

```python
from llm_integration import LLMTaskGenerator, MockLLMProvider

# 使用LLM增强生成
llm_provider = MockLLMProvider(config)
llm_generator = LLMTaskGenerator(config, llm_provider)

# 生成智能任务
enhanced_task = llm_generator.generate_intelligent_task(level=3)
print(f"LLM增强任务: {enhanced_task.description}")
```

### 3. 任务验证

```python
from task_validator import ComprehensiveTaskValidator

# 验证任务
validator = ComprehensiveTaskValidator(config)
validation_result = validator.validate_task(task)

print(f"任务有效性: {validation_result.is_valid}")
print(f"置信度: {validation_result.confidence_score}")
```

### 4. 仿真执行

```python
from sapien_integration import SAPIENEnvironment

# 在SAPIEN中执行任务
simulation_env = SAPIENEnvironment(config)
result = simulation_env.execute_task(task)

print(f"执行成功: {result.success}")
print(f"执行时间: {result.execution_time}秒")
```

### 5. 完整数据收集管道

```python
from data_collection_pipeline import DataCollectionPipeline, PipelineConfig

# 配置数据收集管道
pipeline_config = PipelineConfig(
    num_tasks_per_level={1: 10, 2: 10, 3: 5, 4: 5},
    validation_enabled=True,
    llm_enhancement=True
)

# 运行完整管道
pipeline = DataCollectionPipeline(config, pipeline_config)
results = pipeline.run_full_pipeline()

print(f"数据集保存至: {results['dataset_file']}")
```

## 📊 任务复杂度等级

### Level 1: 基础空间推理
- 简单的物体定位和放置
- 单一物体操作
- 基本的空间位置描述

**示例**: "将红色立方体放置在桌子上"

### Level 2: 相对位置与属性
- 物体属性与位置的结合
- 相对位置关系
- 材质和颜色约束

**示例**: "将金属材质的银色物体放置在桌子的左侧"

### Level 3: 复杂空间关系与推理
- 多物体间的空间关系
- 距离和角度约束
- 对齐和相似性要求

**示例**: "将红色方块与绿色球对齐，距离保持10厘米"

### Level 4: 高级推理与多目标空间推理
- 多目标协调操作
- 复杂的物理约束
- 动态空间布局

**示例**: "协调放置两个物体，确保它们在不同房间但材质相同"

## 🔧 配置说明

### 基础配置 (config.py)

```python
# 物体属性
MATERIALS = ["金属", "塑料", "木质", "玻璃", "陶瓷", ...]
COLORS = ["红色", "蓝色", "绿色", "黄色", ...]
SHAPES = ["立方体", "球体", "圆柱体", ...]

# 空间位置
LOCATIONS = ["桌子", "椅子", "沙发", "床", ...]
RELATIVE_POSITIONS = ["左侧", "右侧", "前方", ...]

# 仿真参数
SIMULATION_CONFIG = {
    "timestep": 1/240,
    "max_episode_length": 1000,
    "camera_resolution": (640, 480)
}
```

### 管道配置 (PipelineConfig)

```python
pipeline_config = PipelineConfig(
    num_tasks_per_level={1: 25, 2: 25, 3: 25, 4: 25},
    validation_enabled=True,
    llm_enhancement=False,
    parallel_execution=True,
    max_workers=4,
    output_directory="collected_data"
)
```

## 🧪 运行示例和测试

```bash
cd tmp
python examples_and_tests.py
```

这将运行所有示例和测试，包括：
- 基础任务生成示例
- LLM增强生成示例
- 任务验证示例
- 仿真执行示例
- 数据收集管道示例
- 完整工作流演示

## 📈 数据收集和存储

框架支持多种数据格式的收集：

- **RGB图像**: 多视角相机图像
- **深度图像**: 深度信息
- **点云数据**: 3D空间信息
- **机器人轨迹**: 关节角度和末端执行器位置
- **任务元数据**: 完整的任务描述和参数

数据以HDF5格式存储，支持压缩和高效访问。

## 🔄 扩展性

### 自定义任务生成器

```python
class CustomTaskGenerator(BaseTaskGenerator):
    def generate_task_input(self, level: int, **kwargs) -> TaskInput:
        # 自定义任务输入生成逻辑
        pass

    def generate_task_description(self, task_input: TaskInput) -> str:
        # 自定义任务描述生成逻辑
        pass
```

### 自定义验证器

```python
class CustomValidator(BaseValidator):
    def validate(self, task: GeneratedTask) -> ValidationResult:
        # 自定义验证逻辑
        pass
```

### 自定义LLM提供者

```python
class CustomLLMProvider(BaseLLMProvider):
    def generate_response(self, prompt: LLMPrompt) -> str:
        # 自定义LLM集成
        pass
```

## 📋 依赖要求

```
numpy
dataclasses (Python < 3.7)
typing_extensions (Python < 3.8)
```

对于完整的SAPIEN集成，还需要：
```
sapien
opencv-python
h5py
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- SAPIEN仿真器团队
- OpenAI GPT模型
- 机器人学习社区的贡献者们