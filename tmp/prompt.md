任务设计框架
我要设计一个Embodied AI Benchmark.关注于单臂manipulation任务，将空间推理任务分为以下几个层级，每个层级都涵盖不同复杂度的任务要求。任务的设计将不仅关注物体的定位（如“目标物在某处”），而是要进一步结合物体的材质、颜色、形状、空间关系、方向等。

1. Level 1: 基础空间推理
这一层次的任务设计主要涉及物体的绝对位置，如物体在某个明确的空间中、或者相对于环境的定位。这些任务通常要求模型识别物体在给定环境中的位置和特征。

任务示例：

目标物在红色木柜内。

目标物在桌子上。

目标物在书架的最上面。

目标物在墙壁旁边。

目标物在床上。

目标物在书房内。

目标物在厨房的水槽旁。

目标物在沙发上。

目标物在窗台上。

目标物在左侧的盒子里。

目标物在门旁。

目标物在客厅的角落。

目标物在窗户下方的桌子上。

目标物在冰箱旁边。

目标物在房间的右角落。

目标物在书桌上的抽屉里。

目标物在床铺的右侧。

目标物在阳台上。

目标物在桌子下面。

目标物在镜子前。

2. Level 2: 相对位置与属性
在这一层次的任务中，物体不仅有明确的空间定位，还需要描述物体的属性，如材质、颜色或环境的相对关系。任务的推理需要结合空间关系和物体特征。

任务示例：

目标物在靠近窗户的空间内，物体是金属材质。

目标物在餐桌上的左侧，物体是塑料材质。

目标物在沙发旁，物体是柔软的。

目标物在桌子上，物体是玻璃材质。

目标物在厨房的柜子里，物体是陶瓷材质。

目标物在书架上，物体是木质的。

目标物在门旁，物体是布料材质。

目标物在床上，物体是棉花材质。

目标物在冰箱附近，物体是铁材质。

目标物在窗台上，物体是塑料材质。

目标物在地板上，物体是皮革材质。

目标物在客厅的右侧，物体是木质的。

目标物在书架下方，物体是金属材质。

目标物在床旁，物体是布料材质。

目标物在餐桌上，物体是纸质的。

目标物在厨房的角落，物体是玻璃材质。

目标物在窗前，物体是陶瓷材质。

目标物在书房的桌子上，物体是塑料材质。

目标物在右边的抽屉里，物体是皮革材质。

目标物在洗衣机旁，物体是布料材质。

3. Level 3: 复杂空间关系与推理
此层次的任务引入了物体间的相对位置关系，如物体与其它物体的关系（如对齐、相似性等）。任务要求模型在空间推理时能够综合考虑多个物体之间的属性和位置关系。

任务示例：

目标物体与另一个空间中的物体材质相同，且不位于房间中央。

目标物体在书架的右侧，且颜色与桌子上的物体相同。

目标物体在沙发旁，且其形状与沙发上的物体相似。

目标物体与一个茶几之间的距离为5厘米，且材质相同。

目标物体位于房间的左侧，不在墙角，且物体与窗台上的物品材质相同。

目标物体在左侧桌子上，且与右侧桌上的物体颜色相同。

目标物体位于厨房的中央，并与其他所有物体材质相同。

目标物体在冰箱旁，且与冰箱上的物体颜色一致。

目标物体与墙面之间的距离是5厘米，且材质与窗帘相同。

目标物体位于书桌上方，且与桌上的书本形状相同。

目标物体在餐厅的右侧，且与墙壁上的物体相似。

目标物体在右侧的抽屉里，且颜色与旁边的杯子相同。

目标物体在客厅的角落，且颜色与地毯上的花纹相同。

目标物体在左侧的椅子上，且材质与旁边的沙发一致。

目标物体位于床下，且颜色与床单的颜色相同。

目标物体位于客厅的中央，且物体的形状与茶几上的物品相似。

目标物体位于厨房的右侧，且与橱柜中的物品材质一致。

目标物体在阳台上，且颜色与窗户上的物品一致。

目标物体位于书架下方，且与书架上的物品材质相同。

目标物体位于洗衣机旁，且颜色与旁边的脏衣篮一致。

4. Level 4: 高级推理与多目标空间推理
这一层次的任务包含多个目标的推理，涉及复杂的空间布局、多物体关系及多属性组合。任务要求模型不仅仅处理单一物体的位置和属性，还要考虑多个物体之间的关系以及物体在动态环境中的交互。

任务示例：

有两个目标，分别在颜色不同但材质相同的空间中。

目标物体分别位于客厅和厨房，并且物体材质相同。

目标物体在两个物体之间，且这两个物体的颜色相同。

目标物体在桌子上，且另一个物体在桌子下，且它们的材质相同。

目标物体位于书架上，而另一个目标物体位于门旁，两者之间距离5厘米。

目标物体在窗台上，且另一个物体在桌子上，且两者的形状和颜色相似。

目标物体在右侧墙壁上，另一个物体在左侧墙壁上，两者材质一致。

目标物体在厨房内，另一个物体在洗手间内，两者材质相同且颜色相近。

有两个目标物体，一个在床旁，一个在沙发旁，且两者材质相同。

目标物体在书架上，另一个目标物体在桌子下方，且两者形状相同。

目标物体在冰箱旁，另一个目标物体在客厅，且两者材质相同。

目标物体在厨房中，另一个物体在餐桌上，且它们的形状和颜色相似。

有两个目标，分别在餐桌旁和茶几旁，且颜色相同。

目标物体在沙发旁，另一个目标物体在茶几旁，且材质一致。

目标物体在墙上，另一个目标物体在地板上，且它们的颜色和材质相同。

目标物体分别位于书桌左侧和右侧，且两者大小相同。

目标物体在床上，另一个目标物体在地面上，两者颜色一致。

目标物体位于餐厅，另一个物体在厨房，两者距离不超过10米。

目标物体在书架上，另一个物体在床旁，两者颜色一致，材质不同。

目标物体在客厅中央，另一个物体在窗台上，两者颜色和形状不同。

希望可以设计LLM自动生成以上任务的Prompt

为了实现LLM自动生成空间推理任务并将其应用于机械臂操控任务，我们可以设计一个详细的框架，涵盖从任务输入、模型架构到任务生成管道的完整实现。以下是一个深入的设计方案：

LLM自动生成空间推理任务的设计框架
1. 任务输入格式设计
为了让LLM生成有效的任务，首先需要定义一个清晰的输入格式。这些输入格式将为LLM提供足够的上下文信息，确保生成的任务满足空间推理和机械臂操作要求。

1.1 输入结构
任务输入结构可以设计为一个JSON对象，包含以下字段：

任务等级（Level）：定义任务的复杂度（Level 1–4）。

空间要求（Spatial Requirements）：明确物体在空间中的相对或绝对位置要求。

物体属性（Object Attributes）：描述任务中物体的材质、颜色、形状等。

约束条件（Constraints）：例如：距离、角度、对齐等空间约束。

环境背景（Environment Context）：如环境中物体的布局，其他物体的存在与否等。

输入样例：

json
复制
编辑
{
  "level": 2,
  "spatial_requirements": {
    "object_1": {"position": "near window", "distance_from_edge": "5 cm"},
    "object_2": {"position": "on the table", "relative_position": "left of object_1"}
  },
  "object_attributes": {
    "object_1": {"material": "metal", "color": "silver"},
    "object_2": {"material": "plastic", "color": "red"}
  },
  "constraints": {
    "alignment": "horizontal",
    "distance": "10 cm"
  },
  "environment_context": {
    "location": "kitchen",
    "objects_in_room": ["table", "window", "metal_can"]
  }
}
1.2 生成目标
LLM输入：上述输入将被转换为一个结构化的文本提示，LLM将根据这些输入信息生成任务。

输出格式：LLM将生成带有清晰任务描述的文本，提供空间推理、物体属性、位置关系及约束。

2. LLM模型架构设计
模型架构需要能够理解任务的复杂性，并能够生成复杂的空间推理任务。我们将使用大语言模型（如GPT-4）来进行任务生成。

2.1 模型输入：
输入由任务描述模板和空间关系描述两部分组成。模型需要同时理解物体的位置、属性、约束关系以及多物体间的空间交互。

2.2 任务生成过程：
阶段1：生成任务描述

LLM首先基于输入生成简洁的任务描述。例如： “将金属瓶放置到窗台上，并确保其与桌子上的物体相对。”

阶段2：根据空间要求和物体属性，生成详细的操作步骤。

“首先抓取金属瓶，将其移动到窗台上，确保瓶子与桌子上的物体保持10cm的距离，并且两者垂直对齐。”

阶段3：考虑约束条件，生成执行步骤。

“抓取物体时，确保物体不碰撞到桌子上的其他物品。”

2.3 LLM生成机制
模型采用基于条件生成的策略：

条件生成：根据输入的任务等级和空间要求，模型会选择合适的推理层次。例如，对于Level 2任务，模型会引入相对位置推理和物体间的属性关系。

3. 任务生成管道
任务生成管道由以下几个阶段组成：

3.1 需求分析：
首先，系统会分析任务输入，解析空间要求、物体属性、环境背景等信息。这些信息将被转化为任务模板中的占位符，以便供LLM填充。

3.2 模板选择：
系统会根据任务等级选择合适的模板。对于简单任务（Level 1），模型生成较为简单的空间定位任务；而对于复杂任务（Level 4），模板会包含更多的物体间约束、动态变化等高级推理要求。

模板示例：

Level 1: “目标物体[物体]放置在[位置]。”

Level 2: “目标物体[物体]放置在[相对位置]，与[物体2]保持[距离]。”

Level 3: “目标物体[物体]放置在[相对位置]，与其他[物体]保持对齐，且材质相同。”

Level 4: “有两个目标物体，分别位于[位置1]和[位置2]，要求[物体1]与[物体2]相对，并且符合[物理约束]。”

3.3 空间推理生成：
LLM根据模板生成详细的任务，并进行空间推理。此阶段模型不仅生成简单的物体放置任务，还会基于输入信息进行空间推理，例如：计算物体间的距离、判断物体是否对齐等。

3.4 复杂度控制：
根据任务的复杂度，管道将调整生成的任务内容。例如，对于Level 2任务，增加物体的相对定位要求；而对于Level 4任务，增加多物体间的空间推理与交互。

3.5 输出生成：
生成最终的任务描述，包括物体的属性、位置关系、约束条件等。任务输出不仅包含任务目标，还包括执行步骤、空间约束、操作指令等。

4. 任务验证流程
4.1 可执行性检查：
生成的任务需要在仿真平台（如Isaac Sim、Habitat等）中进行测试。任务的可执行性检查主要关注以下几个方面：

物体是否能够被正确地抓取。

物体是否能按照任务要求放置到指定位置。

是否满足空间约束（如对齐、距离、角度等）。

4.2 验证反馈：
根据仿真执行的反馈，系统可以对任务进行优化：

任务成功：系统根据验证结果生成新的任务，逐步增加任务复杂度。

任务失败：系统分析任务失败的原因，可能是因为约束条件不明确，或任务设计超出了机器人的能力范围。在这种情况下，任务会被调整并重新生成。

4.3 任务优化：
根据验证结果，调整LLM生成任务的策略，例如通过优化输入模板、增强空间推理的能力或引入更多的物体间交互关系。

5. 多层次优化策略
5.1 基于任务等级优化：
Level 1：简化任务内容，专注于基础的物体放置和抓取。

Level 2：引入物体的相对位置、角度、材质等属性，并确保物体之间的空间关系。

Level 3：增加多个物体之间的交互关系，如重心、对齐、稳定性等。

Level 4：加入多个目标物体和物理约束，要求机器人进行复杂的空间布局和多物体协作。

5.2 模型优化：
根据生成的任务的复杂性和难度，逐步训练LLM以提高任务生成的精确度。例如，通过引入更强大的推理能力，使得模型能够处理更复杂的物理交互和多物体操作。

5.3 自适应调整：
随着任务的生成，系统可以根据执行结果自适应调整任务生成过程。例如，若某些类型的任务失败较多，则可能调整这些任务的复杂度，直到模型在实际环境中能够成功执行。